// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import BasicLayout from '@/layouts/basic-layout';
import Setting from "@/setting";
const pre = 'store_';

export default {
  path: `${Setting.roterPre}/store`,
	name: 'store',
	header: 'store',
	// redirect: {
	// 	name: `${pre}statistics`
	// },
	component: BasicLayout,
	children: [
		{
			path:'system/base',
			name: `${pre}systemBase`,
			meta: {
				auth: ['store-system-base'],
				title: '门店设置'
			},
			component: () => import('@/pages/store/base/index')
		},
		{
			path: 'category/index',
			name: `${pre}category`,
			meta: {
				auth: ['admin-store-store_category'],
				title: '门店分类'
			},
			component: () => import('@/pages/store/storeCategory/index')
		},
		{
			path: 'store_menus/index',
			name: `${pre}storeMenus`,
			meta: {
				auth: ['admin-store_menus-index'],
				title: '门店菜单'
			},
			component: () => import('@/pages/store/storeMenus/index')
		},
		{
			path: 'cashier_menus/index',
			name: `${pre}cashierMenus`,
			meta: {
				auth: ['admin-cashier_menus-index'],
				title: '收银台菜单'
			},
			component: () => import('@/pages/store/cashierMenus/index')
		},
		{
			path: 'statistics',
			name: `${pre}statistics`,
			meta: {
				auth: ['admin-store-store_statistics'],
				title: '运营概况'
			},
			component: () => import('@/pages/store/statistics/index')
		},
		{
			path: 'store/index',
			name: `${pre}storeList`,
			meta: {
				auth: ['admin-store-store_list'],
				title: '门店列表'
			},
			component: () => import('@/pages/store/storeList/index')
		},
		{
			path: 'add_store/:id?',
			name: `${pre}addStore`,
			meta: {
				auth: ['admin-store-add_store'],
				title: '添加门店'
			},
			component: () => import('@/pages/store/addStore/index')
		},
		{
			path: 'region/list',
			name: `${pre}regionList`,
			meta: {
				auth: ['admin-store-region_list'],
				title: '区域列表'
			},
			component: () => import('@/pages/store/region/index')
		},
		{
			path: 'region/create/:id?',
			name: `${pre}addRegion`,
			meta: {
				auth: ['admin-store-region_create'],
				title: '添加区域'
			},
			component: () => import('@/pages/store/region/create')
		},
		{
			path: 'order/index',
			name: `${pre}order`,
			meta: {
				auth: ['admin-store-store_order'],
				title: '门店订单'
			},
			component: () => import('@/pages/store/order/index')
		},
		{
			path: 'refund/order',
			name: `${pre}refundOrder`,
			meta: {
				auth: ['admin-store-store_order'],
				title: '售后订单'
			},
			component: () => import('@/pages/store/refund/order')
		},
		{
			path: 'capital/index',
			name: `${pre}capital`,
			meta: {
				auth: ['admin-store-capital-index'],
				title: '门店流水'
			},
			component: () => import('@/pages/store/capital/index')
		},
		{
			path: 'bill/index',
			name: `${pre}bill`,
			meta: {
				auth: ['admin-store-bill-index'],
				title: '账单记录'
			},
			component: () => import('@/pages/store/bill/index')
		},
		{
			path: 'cash/index',
			name: `${pre}cash`,
			meta: {
				auth: ['admin-store-cash-index'],
				title: '转账申请'
			},
			component: () => import('@/pages/store/cash/index')
		},
		{
			path: 'finance/set/:type?/:tab_id?',
			name: `${pre}setting`,
			meta: {
				auth: ['admin-store-finance-set'],
				title: '财务设置'
			},
			props: {
				typeMole: 'finance'
			},
			component: () => import('@/components/fromSubmit/commonForm.vue')
		},
	]
};
