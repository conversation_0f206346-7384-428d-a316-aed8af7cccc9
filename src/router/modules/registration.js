// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import BasicLayout from '@/layouts/basic-layout';
import Setting from '@/setting';

const pre = 'registration_';

export default {
    path: `${Setting.roterPre}/registration`,
    name: 'registration',
    header: 'registration',
    meta: {
        // 授权标识
        auth: ['admin-store-index']
    },
    component: BasicLayout,
    children: [{
            path: `${Setting.roterPre}/registration/registrationList/index`,
            name: `${pre}registrationList`,
            meta: {
                title: '预约管理',
                auth: ['admin-hospital-registration']
            },
            component: () => import('@/pages/registration/registrationList/index')
        },
        {
            path: `${Setting.roterPre}/registration/registrationList/index`,
            name: `${pre}registrationList`,
            meta: {
                title: '科室管理',
                auth: ['admin-hospital-registration-index']
            },
            component: () => import('@/pages/registration/registrationList/index')
        },
        {
            path: `${Setting.roterPre}/manage/manageList/index`,
            name: `${pre}manageList`,
            meta: {
                title: '挂号信息',
                auth: ['admin-hospital-manage-index']
            },
            component: () => import('@/pages/manage/manageList/index')
        }

    ]
};