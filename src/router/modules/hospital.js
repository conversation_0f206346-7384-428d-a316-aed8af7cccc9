// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import BasicLayout from '@/layouts/basic-layout';
import Setting from '@/setting';

const pre = 'training_';

export default {
    path: `${Setting.roterPre}/training`,
    name: 'training',
    header: 'training',
    meta: {
        // 授权标识
        auth: ['admin-store-index']
    },
    // redirect: {
    //     name: `${pre}productList`
    // },
    component: BasicLayout,
    children: [{
            path: 'training_list',
            name: `${pre}trainingList`,
            meta: {
                title: '康讯中心',
                keepAlive: true,
                scollTopPosition: 0,
                auth: ['admin-hospital-training']
            },
            component: () => import('@/pages/training/trainingList')
        },
        {
            path: 'training_list',
            name: `${pre}trainingList`,
            meta: {
                title: '康讯管理',
                auth: ['admin-store-storeCategory-index']
            },
            component: () => import('@/pages/registration/registrationList/index')
        }

    ]
};