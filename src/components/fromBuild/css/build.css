.input-build{
    margin-bottom: 30px;
}

.upload-build .demo-upload-list{
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: url('../../../assets/images/transparent.jpg') no-repeat;
		background-size: 100% 100%;
    position: relative;
    -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.2);
            box-shadow: 0 1px 1px rgba(0,0,0,.2);
    margin-right: 4px;
    vertical-align: middle;
}
.upload-build .demo-upload-list img{
    width: 100%;
    height: 100%;
}
.upload-build .demo-upload-list-cover{
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
}
.upload-build .demo-upload-list:hover .demo-upload-list-cover{
    display: block;
}
.upload-build .demo-upload-list-cover i{
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    margin: 0 2px;
}

.input-build .input-error-wrapper {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -moz-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    max-width: 100%;
    word-break: break-all;
    vertical-align: middle;
}

.input-build .ivu-input-wrapper, .input-build .ivu-select {
    -webkit-flex-shrink: 0;
        -ms-flex-negative: 0;
            flex-shrink: 0;
    width: 460px;
}

.input-build .error-wrapper {
    padding-left: 14px;
    font-size: 12px;
    color: #ED4014;
}

.input-build .info-wrapper {
    padding-top: 14px;
    font-size: 12px;
    line-height: 12px;
    color: #999999;
}

.input-build-card {
    margin-bottom: 14px;
}

.ivu-form .upload-build .ivu-form-item-label {
    padding: 24px 12px 24px 0;
}

.upload-build .demo-upload-list .ivu-icon {
    vertical-align: middle;
}

.input-build .ivu-input-number {
    width: 223px;
}

.input-build-card .ivu-card-head p {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}
.ivu-checkbox-wrapper {
  font-size: 12px;
}