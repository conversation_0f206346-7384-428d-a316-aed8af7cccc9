<template>
    <!-- 分销-分销员管理 -->
    <div>
        <Card :bordered="false"
              dis-hover
              class="ivu-mt"
              :padding="0">
            <div class="new_card_pd">
                <!-- 查询条件 -->
                <Form ref="formValidate"
                      :model="formValidate"
                      :label-width="labelWidth"
                      :label-position="labelPosition"
                      @submit.native.prevent
                      inline>
                    <FormItem label="时间选择：">
                        <DatePicker :editable="false"
                                    @on-change="onchangeTime"
                                    :value="timeVal"
                                    format="yyyy/MM/dd"
                                    type="datetimerange"
                                    placement="bottom-start"
                                    placeholder="自定义时间"
                                    class="input-add"
                                    :options="options"></DatePicker>
                    </FormItem>
                    <FormItem label="搜索："
                              label-for="status">
                        <Input placeholder="请输入用户信息、电话、ID"
                               v-model="formValidate.nickname"
                               class="input-add mr14" />
                        <Button type="primary"
                                @click="userSearchs"
                                class="mr14">查询</Button>
                        <Button v-auth="['export-userAgent']"
                                class="export"
                                @click="exports">导出</Button>
                    </FormItem>
                </Form>
            </div>
        </Card>
        <cards-data :cardLists="cardLists"
                    v-if="cardLists.length >= 0"></cards-data>
        <Card :bordered="false"
              dis-hover>
            <Table ref="selection"
                   :columns="columns"
                   :data="tableList"
                   class="ivu-mt"
                   :loading="loading"
                   no-data-text="暂无数据"
                   highlight-row
                   no-filtered-data-text="暂无筛选结果">
                <template slot-scope="{ row }"
                          slot="nickname">
                    <div class="name">
                        <div class="item">昵称:{{ row.nickname }}</div>
                        <div class="item">姓名:{{ row.real_name }}</div>
                        <div class="item">电话:{{ row.phone }}</div>
                    </div>
                </template>
                <template slot-scope="{ row }"
                          slot="agentLevel">
                    <div>{{ row.agentLevel ? row.agentLevel.name : "--" }}</div>
                </template>
                <template slot-scope="{ row, index }"
                          slot="right">
                    <a @click="promoters(row, 'man')">推广人</a>
                    <Divider type="vertical" />
                    <template>
                        <Dropdown @on-click="changeMenu(row, $event, index)">
                            <a href="javascript:void(0)">
                                更多
                                <Icon type="ios-arrow-down"></Icon>
                            </a>
                            <DropdownMenu slot="list">
                                <DropdownItem name="1">推广订单</DropdownItem>
                                <DropdownItem name="2">推广方式</DropdownItem>
                                <DropdownItem name="3">赠送分销等级</DropdownItem>
                                <DropdownItem name="4">分配礼品卡</DropdownItem>
                                <DropdownItem name="5">查看礼品卡</DropdownItem>
                            </DropdownMenu>
                        </Dropdown>
                    </template>
                </template>
            </Table>
            <div class="acea-row row-right page">
                <Page :total="total"
                      :current="formValidate.page"
                      show-elevator
                      show-total
                      @on-change="pageChange"
                      :page-size="formValidate.limit" />
            </div>
        </Card>
        <!-- 推广人列表-->
        <promoters-list ref="promotersLists"></promoters-list>
        <!-- 推广方式-->
        <Modal v-model="modals"
               scrollable
               footer-hide
               closable
               title="推广二维码"
               :mask-closable="false"
               width="600">
            <div class="acea-row row-around">
                <div class="acea-row row-column-around row-between-wrapper">
                    <div class="QRpic"
                         v-if="code_src"><img v-lazy="code_src" /></div>
                    <span class="QRpic_sp1 mt10"
                          @click="getWeChat">公众号推广二维码</span>
                </div>
                <div class="acea-row row-column-around row-between-wrapper">
                    <div class="QRpic"
                         v-if="code_xcx"><img v-lazy="code_xcx" /></div>
                    <span class="QRpic_sp2 mt10"
                          @click="getXcx">小程序推广二维码</span>
                </div>
                <div class="acea-row row-column-around row-between-wrapper">
                    <div class="QRpic"
                         v-if="code_h5"><img v-lazy="code_h5" /></div>
                    <span class="QRpic_sp2 mt10"
                          @click="getH5">H5推广二维码</span>
                </div>
            </div>
            <Spin size="large"
                  fix
                  v-if="spinShow"></Spin>
        </Modal>
        <!--修改推广人-->
        <Modal v-model="promoterShow"
               scrollable
               title="修改推广人"
               class="order_box"
               :closable="false">
            <Form ref="formInline"
                  :model="formInline"
                  :label-width="100"
                  @submit.native.prevent>
                <FormItem label="用户头像："
                          prop="image">
                    <div class="picBox"
                         @click="customer">
                        <div class="pictrue"
                             v-if="formInline.image">
                            <img v-lazy="formInline.image" />
                        </div>
                        <div class="upLoad acea-row row-center-wrapper"
                             v-else>
                            <Icon type="ios-camera-outline"
                                  size="26" />
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button type="primary"
                        @click="putSend('formInline')">提交</Button>
                <Button @click="cancel('formInline')">取消</Button>
            </div>
        </Modal>
        <Modal v-model="customerShow"
               scrollable
               title="请选择商城用户"
               :closable="false"
               width="50%">
            <customerInfo @imageObject="imageObject"></customerInfo>
        </Modal>
        <!-- 分配礼品卡 -->
        <Modal v-model="giftModal"
               title="分配礼品卡">
            <Form :model="giftForm"
                  :label-width="80">
                <FormItem label="选择礼品卡">
                    <Select v-model="giftForm.gift_id"
                            style="width:400px"
                            @on-change="fetchCard"
                            clearable
                            filterable>
                        <Option v-for="item in giftList"
                                :key="item.id"
                                :value="item.id">{{ item.name }}</Option>
                    </Select>
                </FormItem>
                <FormItem label="选择卡号">
                    <Select v-model="giftForm.card_numbers"
                            multiple
                            placeholder="请选择卡号"
                            filterable
                            :loading="cardLoading">
                        <Option v-for="item in cardList"
                                :key="item.card_number"
                                :label="item.card_number"
                                :value="item.id" />
                    </Select>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="giftModal=false">取消</Button>
                <Button type="primary"
                        @click="submitGift()">确定</Button>
            </div>
        </Modal>
        <!-- 查看礼品卡 -->
        <Modal v-model="viewGiftModal"
               title="查看礼品卡"
               width="85%"
               :mask-closable="false"
               @on-visible-change="handleVisibleChange">
            <Form :model="viewGiftForm"
                  inline>
                <FormItem label="使用人信息"
                          :label-width="100">
                    <Input v-model="viewGiftForm.use_keyword"
                           placeholder="请输入用户名,电话,ID"
                           style="width:200px"
                           clearable />
                </FormItem>
                <FormItem label="卡号"
                          :label-width="100">
                    <Input v-model="viewGiftForm.card_number"
                           placeholder="请输入卡号"
                           style="width:200px"
                           clearable />
                </FormItem>
                <FormItem label="礼品卡"
                          :label-width="100">
                    <Input v-model="viewGiftForm.card_name"
                           placeholder="请输入礼品卡名称"
                           style="width:200px"
                           clearable />
                </FormItem>
                <FormItem label="状态"
                          :label-width="100">
                    <Select v-model="viewGiftForm.status"
                            clearable
                            filterable
                            style="width:200px">
                        <Option v-for="item in statusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value" />
                    </Select>
                </FormItem>
                <FormItem>
                    <Button type="primary"
                            style="margin-left:20px;"
                            @click="getViewGiftList">搜索</Button>
                    <Button type="primary"
                            style="margin-left:20px;"
                            @click="openAddInViewModal">新增</Button>
                    <Button type="success"
                            @click="exportData"
                            style="margin-left:20px;">导出</Button>
                </FormItem>
            </Form>

            <Table :columns="viewGiftColumns"
                   :data="viewGiftList"
                   :loading="viewGiftLoading"
                   size="small"
                   height="400">
            </Table>

            <div class="acea-row row-right page">
                <Page :total="viewGiftTotal"
                      :current="viewGiftForm.page"
                      :page-size="viewGiftForm.limit"
                      show-elevator
                      show-total
                      @on-change="pageChangeViewGift" />
            </div>
        </Modal>
        <!-- 查看礼品卡里的【新增】专用弹窗 -->
        <Modal v-model="addInViewModal"
               title="新增分配礼品卡">
            <Form :model="addInViewForm"
                  :label-width="80">
                <FormItem label="礼品卡">
                    <Select v-model="addInViewForm.gift_id"
                            placeholder="请选择礼品卡"
                            filterable
                            remote
                            :remote-method="fetchAddInViewGift"
                            @on-change="fetchAddInViewCard">
                        <Option v-for="g in addInViewGiftList"
                                :key="g.id"
                                :label="g.name"
                                :value="g.id" />
                    </Select>
                </FormItem>
                <FormItem label="卡号">
                    <Select v-model="addInViewForm.card_numbers"
                            multiple
                            placeholder="请选择卡号">
                        <Option v-for="c in addInViewCardList"
                                :key="c.card_number"
                                :label="c.card_number"
                                :value="c.id" />
                    </Select>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="addInViewModal=false">取消</Button>
                <Button type="primary"
                        @click="submitAddInView()">确定</Button>
            </div>
        </Modal>

    </div>
</template>

<script>
    import cardsData from "@/components/cards/cards";
    import searchFrom from "@/components/publicSearchFrom";
    import { mapState } from "vuex";
    import exportExcel from "@/utils/newToExcel.js";
    import { membershipDataAddApi } from "@/api/membershipLevel";
    import {
        agentListApi,
        statisticsApi,
        lookCodeApi,
        lookxcxCodeApi,
        lookh5CodeApi,
        userAgentApi,
        agentSpreadApi,
        giftcardlist,
        giftcardnumberlist,
        distribute,
        discardnumberlist,
        discardnumberexport,
        canceldistribute
    } from "@/api/agent";
    import promotersList from "./handle/promotersList";
    import customerInfo from "@/components/customerInfo";
    import timeOptions from "@/utils/timeOptions";
    export default {
        name: "agentManage",
        components: { cardsData, searchFrom, promotersList, customerInfo },
        data () {
            return {
                addInViewModal: false,       // 查看礼品卡里专用的“新增”弹窗开关
                addInViewForm: {             // 专用表单
                    uid: 0,                 // 使用人
                    gift_id: '',
                    card_numbers: []
                },
                addInViewGiftList: [],       // 礼品卡下拉
                addInViewCardList: [],       // 卡号下拉
                viewGiftColumns: [
                    { title: '编号', key: 'id' },
                    { title: '礼品卡名称', key: 'card_name' },
                    { title: '卡号', key: 'card_number' },
                    {
                        title: '使用用户',
                        key: 'nickname',
                        render: (h, { row }) => h('span', row.nickname || '--')
                    },
                    { title: '手机号', key: 'phone' },
                    {
                        title: '状态',
                        key: 'status_text',
                    },
                    {
                        title: '使用时间',
                        key: 'use_time',
                        render: (h, { row }) => h('span', row.use_time || '--')
                    },
                    {
                        title: '操作',
                        render: (h, { row }) => {
                            return h('Button', {
                                props: { type: 'primary', size: 'small' },
                                on: { click: () => this.unbindGift(row) }
                            }, '解除分配');

                        }
                    }
                ],
                viewGiftModal: false,        // 弹窗开关
                viewGiftForm: {
                    dis_uid: '',             // 礼品卡 id
                    card_number: '',         // 卡号关键字
                    status: '',
                    card_name: '',            // 状态 0=未使用 1=已使用
                    page: 1,
                    limit: 10
                },
                viewGiftList: [],           // 表格数据
                viewGiftTotal: 0,           // 总数
                viewGiftLoading: false,
                giftOptions: [],            // 礼品卡下拉
                statusOptions: [
                    { label: '使用', value: 1 },
                    { label: '未使用', value: 0 },
                    { label: '已停用', value: -1 }
                ],
                giftModal: false,           // 弹窗开关
                giftForm: {
                    uid: 0,                // 当前分销员 id
                    gift_id: '',           // 礼品卡 id
                    card_numbers: []       // 卡号数组
                },
                giftList: [],              // 礼品卡下拉数据
                cardList: [],
                customerShow: false,
                promoterShow: false,
                modals: false,
                spinShow: false,
                rows: null,
                grid: {
                    xl: 7,
                    lg: 10,
                    md: 12,
                    sm: 24,
                    xs: 24,
                },
                options: timeOptions,
                formValidate: {
                    nickname: "",
                    data: "",
                    page: 1,
                    limit: 15,
                },
                date: "all",
                total: 0,
                cardLists: [],
                loading: false,
                tableList: [],
                timeVal: [],
                columns: [
                    {
                        title: "ID",
                        key: "uid",
                        sortable: true,
                        width: 80,
                    },
                    {
                        title: "头像",
                        key: "headimgurl",
                        minWidth: 60,
                        render: (h, params) => {
                            return h("viewer", [
                                h(
                                    "div",
                                    {
                                        style: {
                                            width: "36px",
                                            height: "36px",
                                            borderRadius: "4px",
                                            cursor: "pointer",
                                        },
                                    },
                                    [
                                        h("img", {
                                            attrs: {
                                                src: params.row.headimgurl
                                                    ? params.row.headimgurl
                                                    : require("../../assets/images/moren.jpg"),
                                            },
                                            style: {
                                                width: "100%",
                                                height: "100%",
                                            },
                                        }),
                                    ]
                                ),
                            ]);
                        },
                    },
                    {
                        title: "用户信息",
                        slot: "nickname",
                        minWidth: 120,
                    },
                    {
                        title: "推广用户数量",
                        key: "spread_count",
                        minWidth: 125,
                    },
                    {
                        title: "订单数量",
                        key: "order_count",
                        minWidth: 90,
                    },
                    {
                        title: "订单金额",
                        key: "order_price",
                        minWidth: 120,
                    },
                    {
                        title: "分销等级",
                        slot: "agentLevel",
                        minWidth: 120,
                    },
                    {
                        title: "账户佣金",
                        key: "brokerage_money",
                        minWidth: 120,
                    },
                    {
                        title: "已提现金额",
                        key: "extract_count_price",
                        minWidth: 120,
                    },
                    {
                        title: "提现次数",
                        key: "extract_count_num",
                        minWidth: 100,
                    },
                    {
                        title: "未提现金额",
                        key: "new_money",
                        minWidth: 105,
                    },
                    {
                        title: "上级推广人",
                        key: "spread_name",
                        minWidth: 105,
                    },
                    {
                        title: "操作",
                        slot: "right",
                        minWidth: 130,
                    },
                ],
                code_src: "",
                code_xcx: "",
                code_h5: "",
                formInline: {
                    uid: 0,
                    spread_uid: 0,
                    image: "",
                },
            };
        },
        computed: {
            ...mapState("admin/layout", ["isMobile"]),
            labelWidth () {
                return this.isMobile ? undefined : 96;
            },
            labelPosition () {
                return this.isMobile ? "top" : "right";
            },
        },
        created () {
            this.getList();
            this.getStatistics();
        },
        methods: {
            exportData () {
                // const query = this.addInViewForm;
                discardnumberexport(this.viewGiftForm.dis_uid).then(res => {
                    const url = res.data
                    window.open(url, '_blank');
                })
            },
            // 从“查看礼品卡”里点击【新增】按钮
            openAddInViewModal () {
                this.addInViewForm = { uid: this.viewGiftForm.dis_uid, gift_id: '', card_numbers: [] };
                this.addInViewGiftList = [];
                this.addInViewCardList = [];
                this.addInViewModal = true;
                this.fetchAddInViewGift();   // 拉礼品卡
            },

            // 拉礼品卡（独立）
            fetchAddInViewGift (query = '') {
                giftcardlist({ keyword: query }).then(res => {
                    this.addInViewGiftList = res.data;
                });
            },

            // 礼品卡变化 → 拉卡号（独立）
            fetchAddInViewCard (giftId) {
                if (!giftId) {
                    this.addInViewCardList = [];
                    return;
                }
                giftcardnumberlist({ gift_card_id: giftId }).then(res => {
                    this.addInViewCardList = res.data;
                });
            },

            // 提交（独立）
            submitAddInView () {
                console.log(this.addInViewForm, '罗鑫')
                if (!this.addInViewForm.gift_id || !this.addInViewForm.card_numbers.length) {
                    this.$Message.warning('请完成选择');
                    return;
                }
                const params = {
                    gift_card_id: this.addInViewForm.gift_id,
                    gift_card_number_ids: (this.addInViewForm.card_numbers).join(','),
                    dis_uid: this.viewGiftForm.dis_uid
                };
                distribute(params).then(res => {
                    this.$Message.success('新增成功');
                    this.addInViewModal = false;
                    this.getViewGiftList();   // 刷新查看列表
                });
            },
            // 打开弹窗
            openViewGiftModal (row) {
                this.viewGiftForm = {
                    dis_uid: row.uid,
                    card_number: '',
                    use_keyword: '',
                    card_name: '',
                    status: '',
                    page: 1,
                    limit: 10
                };
                this.viewGiftModal = true;
                // this.fetchGiftOptions();   // 先拉礼品卡
                this.getViewGiftList();    // 再拉列表
            },

            // 拉礼品卡下拉
            fetchGiftOptions (query = '') {
                this.$api.giftSearch({ keyword: query }).then(res => {
                    this.giftOptions = res.data;
                });
            },

            // 拉列表
            getViewGiftList () {
                this.viewGiftLoading = true;
                // 替换成你的接口
                discardnumberlist(this.viewGiftForm).then(res => {
                    this.viewGiftList = res.data.list;
                    this.viewGiftTotal = res.data.count;
                }).finally(() => {
                    this.viewGiftLoading = false;
                });
            },

            // 分页
            pageChangeViewGift (page) {
                this.viewGiftForm.page = page;
                this.getViewGiftList();
            },

            // 解除分配
            unbindGift (row) {
                console.log(row, 'dagd')
                this.$Modal.confirm({
                    title: '提示',
                    content: '确定解除该礼品卡的分配吗？',
                    onOk: () => {
                        const data = {
                            gift_card_number_id: row.gift_card_number_id,
                            dis_uid: this.viewGiftForm.dis_uid
                        }
                        canceldistribute(data).then(res => {
                            this.$Message.success('已解除');
                            this.getViewGiftList();
                        });
                    }
                });
            },
            // 打开弹窗
            openGiftModal (row) {
                this.giftForm.uid = row.uid;
                this.giftForm.gift_id = '';
                this.giftForm.card_numbers = [];
                this.giftList = [];
                this.cardList = [];
                this.giftModal = true;
                this.fetchGift();      // 先拉一次礼品卡列表
            },

            // 拉取礼品卡
            fetchGift () {
                this.giftLoading = true;
                // 替换为你的接口
                giftcardlist().then(res => {
                    this.giftList = res.data;
                }).finally(() => this.giftLoading = false);
            },

            // 礼品卡变化 → 拉取可用卡号
            fetchCard (giftId) {
                if (!giftId) {
                    this.cardList = [];
                    return;
                }
                this.cardLoading = true;
                // 替换为你的接口
                giftcardnumberlist({ gift_card_id: giftId }).then(res => {
                    this.cardList = res.data;
                }).finally(() => this.cardLoading = false);
            },

            // 提交分配
            submitGift () {
                console.log(this.giftForm, '罗鑫')
                if (!this.giftForm.gift_id || !this.giftForm.card_numbers.length) {
                    this.$Message.warning('请完成选择');
                    return;
                }
                // 替换为你的添加接口
                const params = {
                    gift_card_id: this.giftForm.gift_id,
                    gift_card_number_ids: (this.giftForm.card_numbers).join(','),
                    dis_uid: this.giftForm.uid
                }
                distribute(params).then(res => {
                    this.$Message.success('分配成功');
                    this.giftModal = false;
                    this.getList();   // 刷新列表
                });
            },
            // 提交
            putSend (name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        if (!this.formInline.spread_uid) {
                            return this.$Message.error("请上传用户");
                        }
                        agentSpreadApi(this.formInline)
                            .then((res) => {
                                this.promoterShow = false;
                                this.$Message.success(res.msg);
                                this.getList();
                                this.$refs[name].resetFields();
                            })
                            .catch((res) => {
                                this.$Message.error(res.msg);
                            });
                    }
                });
            },
            // 数据导出；
            async exports () {
                let [th, filekey, data] = [[], [], []];
                let fileName = "";
                let excelData = JSON.parse(JSON.stringify(this.formValidate));
                excelData.page = 1;
                for (let i = 0; i < excelData.page + 1; i++) {
                    let lebData = await this.getExcelData(excelData);
                    if (!fileName) fileName = lebData.filename;
                    if (!filekey.length) {
                        filekey = lebData.filekey;
                    }
                    if (!th.length) th = lebData.header;
                    if (lebData.export.length) {
                        data = data.concat(lebData.export);
                        excelData.page++;
                    } else {
                        exportExcel(th, filekey, fileName, data);
                        return;
                    }
                }
            },
            getExcelData (excelData) {
                return new Promise((resolve, reject) => {
                    userAgentApi(excelData).then((res) => {
                        return resolve(res.data);
                    });
                });
            },
            // 操作
            changeMenu (row, name, index) {
                switch (name) {
                    case "1":
                        this.promoters(row, "order");
                        break;
                    case "2":
                        this.spreadQR(row);
                        break;
                    case "3":
                        this.$modalForm(
                            membershipDataAddApi({ uid: row.uid }, "/agent/get_level_form")
                        ).then(() => this.getList());
                    case "4":
                        this.openGiftModal(row);
                        break;
                    case "5":
                        this.openViewGiftModal(row);
                        break;
                }
            },
            customer () {
                this.customerShow = true;
            },
            imageObject (e) {
                this.customerShow = false;
                this.formInline.spread_uid = e.uid;
                this.formInline.image = e.image;
            },
            // 删除
            del (row, tit, num) {
                let delfromData = {
                    title: tit,
                    num: num,
                    url: `agent/stair/delete_spread/${row.uid}`,
                    method: "PUT",
                    ids: "",
                };
                this.$modalSure(delfromData)
                    .then((res) => {
                        this.$Message.success(res.msg);
                        this.getList();
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                    });
            },
            edit (row) {
                this.promoterShow = true;
                this.formInline.uid = row.uid;
            },
            cancel (name) {
                this.promoterShow = false;
                this.$refs[name].resetFields();
            },
            // 推广人列表 订单
            promoters (row, tit) {
                this.$refs.promotersLists.modals = true;
                this.$refs.promotersLists.getList(row, tit);
            },
            // 统计
            getStatistics () {
                let data = {
                    nickname: this.formValidate.nickname,
                    data: this.formValidate.data,
                };
                statisticsApi(data)
                    .then(async (res) => {
                        let data = res.data;
                        data.res.forEach(value => value.col = 4)
                        this.cardLists = data.res;
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                    });
            },
            // 具体日期
            onchangeTime (e) {
                this.timeVal = e;
                this.formValidate.data = this.timeVal[0] ? this.timeVal.join("-") : "";
                this.formValidate.page = 1;
                if (!e[0]) {
                    this.formValidate.data = "";
                }
                this.getList();
                this.getStatistics();
            },
            // 选择时间
            selectChange (tab) {
                this.formValidate.page = 1;
                this.formValidate.data = tab;
                this.timeVal = [];
                this.getList();
                this.getStatistics();
            },
            // 列表
            getList () {
                this.loading = true;
                agentListApi(this.formValidate)
                    .then(async (res) => {
                        let data = res.data;
                        this.tableList = data.list;
                        this.total = res.data.count;
                        this.loading = false;
                    })
                    .catch((res) => {
                        this.loading = false;
                        this.$Message.error(res.msg);
                    });
            },
            pageChange (index) {
                this.formValidate.page = index;
                this.getList();
            },
            // 表格搜索
            userSearchs () {
                this.formValidate.page = 1;
                this.getList();
                this.getStatistics();
            },
            // 二维码
            spreadQR (row) {
                this.modals = true;
                this.rows = row;
            },
            // 公众号推广二维码
            getWeChat () {
                this.spinShow = true;
                let data = {
                    uid: this.rows.uid,
                    action: "wechant_code",
                };
                lookCodeApi(data)
                    .then(async (res) => {
                        let data = res.data;
                        this.code_src = data.code_src;
                        this.spinShow = false;
                    })
                    .catch((res) => {
                        this.spinShow = false;
                        this.$Message.error(res.msg);
                    });
            },
            // 小程序推广二维码
            getXcx () {
                this.spinShow = true;
                let data = {
                    uid: this.rows.uid,
                };
                lookxcxCodeApi(data)
                    .then(async (res) => {
                        let data = res.data;
                        this.code_xcx = data.code_src;
                        this.spinShow = false;
                    })
                    .catch((res) => {
                        this.spinShow = false;
                        this.$Message.error(res.msg);
                    });
            },
            getH5 () {
                this.spinShow = true;
                let data = {
                    uid: this.rows.uid,
                };
                lookh5CodeApi(data)
                    .then(async (res) => {
                        let data = res.data;
                        this.code_h5 = data.code_src;
                        this.spinShow = false;
                    })
                    .catch((res) => {
                        this.spinShow = false;
                        this.$Message.error(res.msg);
                    });
            },
        },
    };
</script>
<style scoped lang="stylus">
    .picBox
        display inline-block
        cursor pointer
        .upLoad
            width 58px
            height 58px
            line-height 58px
            border 1px dotted rgba(0, 0, 0, 0.1)
            border-radius 4px
            background rgba(0, 0, 0, 0.02)
        .pictrue
            width 60px
            height 60px
            border 1px dotted rgba(0, 0, 0, 0.1)
            margin-right 10px
            img
                width 100%
                height 100%
        .iconfont
            color #898989
    .QRpic
        width 180px
        height 180px
        img
            width 100%
            height 100%
    .QRpic_sp1
        font-size 13px
        color #19be6b
        cursor pointer
    .QRpic_sp2
        font-size 13px
        color #2d8cf0
        cursor pointer
    img
        height 36px
        display block
    .ivu-mt .name .item
        margin 3px 0
    .tabform
        margin-bottom 10px
    .Refresh
        font-size 12px
        color #1890FF
        cursor pointer
    .ivu-form-item
        margin-bottom 10px
    /* .ivu-mt >>> .ivu-table-header */
    /* border-top:1px dashed #ddd!important */
</style>