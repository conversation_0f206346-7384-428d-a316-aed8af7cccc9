<template>
<!-- 分销-分销说明 -->
  <div class="form-submit">
    <Card :bordered="false" dis-hover class="ivu-mt">
      <Form :label-width="80" @submit.native.prevent>
        <FormItem label="说明内容：">
          <WangEditor
            :content="content"
            @editorContent="getEditorContent"
          ></WangEditor>
        </FormItem>
      </Form>
      <Spin fix v-if="spinShow"></Spin>
    </Card>
      <Card :bordered="false" dis-hover class="fixed-card" :style="{left: `${!menuCollapse?'236px':isMobile?'0':'60px'}`}">
			<Form>
        <FormItem>
          <Button type="primary" @click="agentAgreementSave">保存</Button>
        </FormItem>
			</Form>
		</Card>	
  </div>
</template>

<script>
import { mapState } from "vuex";
import WangEditor from "@/components/wangEditor/index.vue";
import { agentAgreement, agentAgreementSave } from "@/api/user";

export default {
  name: 'agentAgreement',
  components: { WangEditor },
	computed: {
	  ...mapState("admin/layout", ["isMobile","menuCollapse"])
	},
  data() {
    return {
      id: 0,
      agent: {
        content: "",
        status: 1,
      },
      content:'',
      spinShow: false,
    };
  },
  created() {
    this.agentAgreement();
  },
  methods: {
    getEditorContent(data) {
      this.agent.content = data;
    },
    agentAgreement() {
      this.spinShow = true;
      agentAgreement()
        .then((res) => {
          this.spinShow = false;
          const { title, content, status, id } = res.data;
          this.agent.content = content;
          this.content = content;
          this.agent.status = status;
          this.id = id || 0;
        })
        .catch((err) => {
          this.$Message.error(err);
          this.spinShow = false;
        });
    },
    // 保存
    agentAgreementSave() {
      this.$Spin.show();
      agentAgreementSave(this.id, this.agent)
        .then((res) => {
          this.$Spin.hide();
          this.$Message.success("保存成功");
          this.agentAgreement();
        })
        .catch((err) => {
          this.$Spin.hide();
          this.$Message.error(err.msg);
        });
    },
  },
};
</script>

<style scoped lang="stylus">
	/deep/.ivu-card-body{
		padding-top 40px;
	}
	.form-submit {
		  /deep/.ivu-card{
		  	border-radius: 0;
		  }
	    margin-bottom: 79px;
	
	    .fixed-card {
	        position: fixed;
	        right: 0;
	        bottom: 0;
	        left: 200px;
	        z-index: 99;
	        box-shadow: 0 -1px 2px rgb(240, 240, 240);
	
	        /deep/ .ivu-card-body {
	            padding: 15px 16px 14px;
	        }
	
	        .ivu-form-item {
	            margin-bottom: 0;
	        }
	
	        /deep/ .ivu-form-item-content {
	            margin-right: 124px;
	            text-align: center;
	        }
	
	        .ivu-btn {
	            height: 36px;
	            padding: 0 20px;
	        }
	    }
	}
</style>
