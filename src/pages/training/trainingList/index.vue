<template>
    <!-- 商品-商品分类 -->
    <div class="article-manager">
        <Card :bordered="false"
              dis-hover
              class="ivu-mt"
              :padding="0">
            <div class="new_card_pd">
                <!-- 筛选条件 -->
                <Form ref="artFrom"
                      :model="artFrom"
                      :label-width="labelWidth"
                      inline
                      :label-position="labelPosition"
                      @submit.native.prevent>
                    <FormItem label="就诊人："
                              label-for="status2">
                        <Input placeholder="请输入"
                               v-model="artFrom.patient"
                               class="input-add mr14" />
                    </FormItem>
                    <!-- <FormItem label="手机号："
                              label-for="status2">
                        <Input placeholder="请输入"
                               v-model="artFrom.phone"
                               class="input-add mr14" />
                    </FormItem> -->
                    <FormItem label="康训项目：">
                        <Select v-model="artFrom.project_name"
                                style="width:200px"
                                clearable
                                filterable>
                            <Option v-for="item in training_project"
                                    :key="item.value"
                                    :value="item.value">{{ item.label }}</Option>
                        </Select>
                    </FormItem>
                    <Button type="primary"
                            @click="userSearchs()">查询</Button>
                    <Button class="ResetSearch"
                            style="margin-left: 20px;"
                            @click="reset('artFrom')">重置</Button>
                </Form>
            </div>
        </Card>
        <Card :bordered="false"
              dis-hover
              class="ivu-mt">
            <!-- 相关操作 -->
            <div>
                <Button type="primary"
                        class="bnt"
                        @click="openModal">添加康训记录</Button>
            </div>
            <!-- 商品分类表格 -->
            <vxe-table :data="tableData"
                       ref="xTable"
                       class="ivu-mt"
                       highlight-hover-row
                       :loading="loading"
                       header-row-class-name="false">
                <vxe-table-column field="training_time"
                                  title="康训时间"
                                  min-width="150"
                                  tooltip></vxe-table-column>
                <vxe-table-column field="patient_name"
                                  min-width="50"
                                  title="就诊人"></vxe-table-column>
                <vxe-table-column field="gender"
                                  min-width="50"
                                  title="性别"></vxe-table-column>
                <vxe-table-column field="training_project_text"
                                  title="康训项目"
                                  min-width="150"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="remark"
                                  title="备注"
                                  min-width="150"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="operator_name"
                                  title="康训师"
                                  min-width="150"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="date"
                                  title="操作"
                                  min-width="250"
                                  align="left">
                    <template v-slot="{ row, index }">
                        <a @click="edit(row)">编辑</a>
                        <Divider type="vertical" />
                        <a @click="showDetail(row)">详情</a>
                    </template>
                </vxe-table-column>
            </vxe-table>
            <div class="ivu-mt ivu-text-right">
                <Page :total="total"
                      :current="artFrom.page"
                      :page-size="artFrom.limit"
                      @on-change="changePatientPages"
                      show-elevator
                      show-total />
            </div>
        </Card>

        <!-- 弹窗 -->
        <Modal v-model="showEditModal"
               :title="editId ? '编辑康训记录' : '添加康训记录'"
               width="45%"
               @on-cancel="resetFormModal">
            <Form :model="formModal"
                  :label-width="90">
                <FormItem label="选择就诊人"
                          style="width:600px">
                    <Input v-model="formModal.patient"
                           placeholder="请选择就诊人"
                           readonly />
                    <div @click="openPatientModal()"
                         style="float: right;position: absolute;right: 8%;top: 8%;font-size: 14px;color:#2D8CF0;">选择</div>
                </FormItem>

                <FormItem label="康训时间">
                    <DatePicker v-model="formModal.training_time"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                placeholder="请选择时间" />
                </FormItem>
                <div v-for="(item, index) in formModal.training_project"
                     :key="index"
                     class="project-time-row">
                    <FormItem label="选择康训项目">
                        <Select v-model="item.name"
                                style="width: 200px"
                                clearable
                                filterable>
                            <Option v-for="name in training_project"
                                    :key="name.value"
                                    :value="name.value">{{ name.label }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="时间(分钟)">
                        <Input v-model="item.minute"
                               type="number"
                               placeholder="请输入项目时间" />
                    </FormItem>
                    <Button v-if="index > 0"
                            type="error"
                            class="delete-project-button"
                            style="margin-left: 20px;"
                            @click="removeProject(index)">删除</Button>
                </div>
                <Button type="dashed"
                        class="add-project-button"
                        @click="addProject">
                    <span style="font-size:15px;">+</span>
                    &nbsp;新增康训项目
                </Button>
                <FormItem label="服务备注">
                    <Input v-model="formModal.remark"
                           type="textarea"
                           maxlength="200"
                           rows="4"
                           placeholder="请输入服务情况不超过200字..."
                           show-word-limit />
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="showEditModal = false">取消</Button>
                <Button type="primary"
                        @click="handleOk()">确定</Button>
            </div>
        </Modal>
        <!-- 就诊人选择弹窗 -->
        <Modal v-model="showPatientModal"
               title="选择就诊人"
               width="60%">
            <Form :model="patientSearchForm"
                  inline>
                <FormItem label="就诊人"
                          :label-width="60">
                    <Input v-model="patientSearchForm.patient"
                           placeholder="请输入就诊人" />
                </FormItem>
                <FormItem label="手机号"
                          :label-width="60">
                    <Input v-model="patientSearchForm.phone"
                           placeholder="请输入手机号" />
                </FormItem>
                <FormItem label="身份证号"
                          :label-width="60">
                    <Input v-model="patientSearchForm.id_card"
                           placeholder="请输入身份证号" />
                </FormItem>
                <Button type="primary"
                        @click="searchPatient">查询</Button>
                <Button class="ResetSearch"
                        @click="resets('patientSearchForm')"
                        style="margin-left: 10px;">重置</Button>
            </Form>
            <vxe-table :data="patientTableData"
                       ref="patientTable"
                       class="ivu-mt"
                       highlight-hover-row
                       :loading="patientLoading"
                       header-row-class-name="false">
                <vxe-table-column width="60">
                    <template v-slot="{ row, rowIndex }">
                        <input type="radio"
                               name="patientRadio"
                               :value="rowIndex"
                               v-model="selectedPatientIndex" />
                    </template>
                </vxe-table-column>
                <vxe-table-column field="patient_name"
                                  title="就诊人"></vxe-table-column>
                <vxe-table-column field="phone_number"
                                  title="手机号"></vxe-table-column>
                <vxe-table-column field="id_card"
                                  title="身份证号"></vxe-table-column>
            </vxe-table>
            <div class="ivu-mt ivu-text-right">
                <Page :total="patientTotal"
                      :current="patientSearchForm.page"
                      :page-size="patientSearchForm.limit"
                      @on-change="changePatientPage"
                      show-elevator
                      show-total />
            </div>
            <div slot="footer">
                <Button @click="showPatientModal = false">取消</Button>
                <Button type="primary"
                        @click="confirmPatient">确定</Button>
            </div>
        </Modal>
        <!-- 详情弹窗 -->
        <!-- 详情弹窗 -->
        <Modal v-model="showDetailModal"
               title="康训详情"
               width="45%"
               :mask-closable="false">
            <Form :label-width="90">
                <!-- 一排显示 -->
                <Row :gutter="16">
                    <Col span="8">
                    <FormItem label="就诊人："
                              class="mb0">{{ detailForm.patient }}</FormItem>
                    </Col>
                    <Col span="6">
                    <FormItem label="性别："
                              class="mb0">{{ detailForm.gender }}</FormItem>
                    </Col>
                    <Col span="10">
                    <FormItem label="身份证："
                              class="mb0">{{ detailForm.idCard }}</FormItem>
                    </Col>
                </Row>

                <FormItem label="康训时间：">{{ detailForm.training_time }}</FormItem>

                <FormItem label="康训项目：">
                    <div v-for="(item, idx) in detailForm.training_project"
                         :key="idx">
                        {{ item.name }} - {{ item.minute }}分钟
                    </div>
                </FormItem>

                <FormItem label="服务备注：">{{ detailForm.remark }}</FormItem>
            </Form>

            <div slot="footer">
                <Button type="primary"
                        @click="showDetailModal = false">关闭</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import { gethealthtrainingList, getuserpatient, gettraining_project, addhealthtraining, updatehealthtraining, gethealthtrainingDetail } from "@/api/user";

    export default {
        name: 'product_productClassify',
        data () {
            return {
                showDetailModal: false,   // 详情弹窗开关
                detailForm: {             // 详情回显数据
                    patient: '',
                    gender: '',
                    idCard: '',
                    training_time: '',
                    training_project: [],
                    remark: ''
                },
                showEditModal: false,      // 1. 控制编辑弹窗
                editId: null,              // 2. 当前正在编辑的行主键

                patientTotal: 0,
                patientSearchForm: {
                    patient: '',
                    phone: '',
                    id_card: '',
                    page: 1,
                    limit: 5,
                },
                patientTableData: [],
                patientLoading: false,
                selectedPatientIndex: null,
                showPatientModal: false,
                treeSelect: [],
                FromData: null,
                grid: {
                    xl: 7,
                    lg: 7,
                    md: 12,
                    sm: 24,
                    xs: 24
                },
                loading: false,
                artFrom: {
                    page: 1,
                    patient: '',
                    limit: 10,
                    phone: '',
                    project_name: ''   // 新增：康训项目
                },
                total: 0,
                tableData: [],
                showModal: false,
                formModal: {
                    training_project: [
                        {
                            name: '',
                            minute: ''
                        }
                    ],
                    remark: '',
                    training_time: ''
                },
                training_project: []
            }
        },
        computed: {
            ...mapState('admin/layout', [
                'isMobile'
            ]),
            ...mapState('admin/userLevel', [
                'categoryId'
            ]),
            labelWidth () {
                return this.isMobile ? undefined : 96;
            },
            labelPosition () {
                return this.isMobile ? 'top' : 'right';
            }
        },
        mounted () {
            gettraining_project().then(res => {
                this.training_project = res.data.map(item => ({
                    value: item,
                    label: item
                }));
            });
            this.getList();
        },
        methods: {
            async showDetail (row) {
                this.showDetailModal = true;

                // 调接口
                const { data } = await gethealthtrainingDetail(row.id);
                this.detailForm = {
                    patient: data.patientInfo.patient_name,
                    gender: data.gender,
                    idCard: data.card_no,
                    training_time: data.training_time,
                    training_project: data.training_project,  // 数组
                    remark: data.remark
                };
            },
            showPatientModal () {
                this.showModal = false
            },
            resets () {
                this.patientSearchForm.patient = ''
                this.patientSearchForm.phone = ''
                this.patientSearchForm.id_card = ''
                this.searchPatient()
            },
            changePatientPage (page) {
                this.patientSearchForm.page = page;
                this.searchPatient();
            },
            changePatientPages (page) {
                this.artFrom.page = page;
                this.getList();
            },
            openPatientModal () {
                this.showPatientModal = true;
                this.patientSearchForm.page = 1; // 重置为第一页
                this.searchPatient();

            },
            searchPatient () {
                this.patientLoading = true;
                getuserpatient(this.patientSearchForm).then(res => {
                    this.patientTableData = res.data.list;
                    this.patientTotal = res.data.count;
                    this.patientLoading = false;
                }).catch(err => {
                    this.patientLoading = false;
                    this.$Message.error(err.msg);
                });
            },
            confirmPatient () {
                if (this.selectedPatientIndex !== null) {
                    const selectedPatient = this.patientTableData[this.selectedPatientIndex];
                    this.formModal.patient = selectedPatient.patient_name
                    this.formModal.card_no = selectedPatient.card_no
                    console.log(selectedPatient.card_no, '罗鑫大哥哥')
                    this.showPatientModal = false;
                } else {
                    this.$Message.warning('请选择一个就诊人');
                }
            },
            // 列表
            getList () {
                this.loading = true;
                gethealthtrainingList(this.artFrom).then(async res => {
                    this.tableData = res.data.list;
                    this.total = res.data.count;
                    this.loading = false;
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                })
            },
            pageChange (index) {
                this.artFrom.page = index;
                this.getList();
            },
            reset () {
                this.artFrom.patient = ''
                // this.artFrom.phone = ''
                this.artFrom.project_name = '';  // 新增
                this.getList(this.artFrom);
            },

            // 删除
            del (row, tit, num) {
                let delfromData = {
                    title: tit,
                    num: num,
                    url: `product/category/${row.id}`,
                    method: 'DELETE',
                    ids: ''
                };
                this.$modalSure(delfromData).then((res) => {
                    this.$Message.success(res.msg);
                    this.getList();
                }).catch(res => {
                    this.$Message.error(res.msg);
                });
            },
            // 表格搜索
            userSearchs () {
                this.artFrom.page = 1;
                this.getList();
            },
            // 打开弹窗
            openModal () {
                this.showEditModal = true;
                this.editId = null;            // 保证是新增
                this.resetFormModal();         // 把表单清空
                // 如果下拉数据没提前取，这里再取一次
                gettraining_project().then(res => {
                    this.training_project = res.data.map(item => ({ value: item, label: item }));
                });
                this.resetFormModal();
            },
            // 弹窗确认
            /* 1. 点击表格【编辑】按钮 */
            async edit (row) {
                this.editId = row.id;                 // 主键，根据你真实字段改
                this.showEditModal = true;

                // 调用回显接口
                const { data } = await gethealthtrainingDetail(row.id); // 换成你自己的接口
                // 示例返回结构：{training_time,patient_name,card_no,training_project:[{name,minute}],remark}

                console.log(data, '罗鑫')
                this.formModal = {
                    training_time: data.training_time,
                    patient: data.patientInfo.patient_name,
                    card_no: data.card_no,
                    training_project: data.training_project,
                    remark: data.remark
                };
            },

            /* 2. 新增/编辑共用的“确定”按钮 */
            async handleOk () {
                const formatDate = d => {
                    if (!d) return '';
                    const dt = new Date(d);
                    return `${dt.getFullYear()}-${String(dt.getMonth() + 1).padStart(2, '0')}-${String(dt.getDate()).padStart(2, '0')} ${String(dt.getHours()).padStart(2, '0')}:${String(dt.getMinutes()).padStart(2, '0')}`;
                };

                const postData = { ...this.formModal };
                postData.training_time = formatDate(this.formModal.training_time);

                // 根据 editId 判断是新增还是编辑
                const api = this.editId
                    ? updatehealthtraining({ id: this.editId, ...postData }) // 现在  // 编辑接口
                    : addhealthtraining(postData);                 // 新增接口

                const res = await api;
                this.$Message.success(res.msg);
                this.showEditModal = false;
                this.resetFormModal();
                this.getList();
            },

            /* 3. 重置表单 & 清空 editId */
            resetFormModal () {
                this.formModal = {
                    training_project: [{ name: '', minute: '' }],
                    remark: '',
                    training_time: '',
                    patient: '',
                    card_no: ''
                };
                this.editId = null;
                this.selectedPatientIndex = null; // 顺便把就诊人选中状态也清掉
            },
            // 重置formModal对象
            resetFormModal () {
                this.formModal = {
                    training_project: [
                        {
                            name: '',
                            minute: ''
                        }
                    ],
                    remark: ''
                };
            },
            // 新增康训项目
            addProject () {
                this.formModal.training_project.push({
                    name: '',
                    minute: ''
                });
            },
            // 删除康训项目
            removeProject (index) {
                this.formModal.training_project.splice(index, 1);
            }
        }
    }
</script>
<style scoped lang="stylus">
    .treeSel >>>.ivu-select-dropdown-list
        padding 0 10px !important
        box-sizing border-box
    .tabBox_img
        width 36px
        height 36px
        border-radius 4px
        cursor pointer
        img
            width 100%
            height 100%
    /deep/.ivu-input
        font-size 14px !important
    .project-time-row
        display flex
        margin-bottom 10px
    .add-project-button
        margin-left 90px
        margin-bottom 30px
        border solid 1px #2D8CF0
        color #2D8CF0
        line-hight 10px
        .suffix
            position absolute
            right 10px
            top 50%
            transform translateY(-50%)
            font-size 14px
            color #1A1A1A
            pointer-events none
</style>