<template>
    <div>
    <Card dis-hover class="ivu-mt">
      <div class="new_tab">
        <Tabs value="name1">
            <TabPane label="数据库列表" name="name1">
                <Card  :bordered="false" dis-hover >
                    <div slot="title">
                        <Button type="primary" class="mr10" @click="getBackup">备份</Button>
                        <Button type="primary" class="mr10" @click="getOptimize">优化表</Button>
                        <Button type="primary" class="mr10" @click="getRepair">修复表</Button>
                        <Button type="primary" class="mr10"  @click="exportData(1)">导出文件</Button>
                    </div>
                    <Table ref="selection" :columns="columns" :data="tabList2" :loading="loading"
                        highlight-row  no-data-text="暂无数据"  @on-selection-change="onSelectTab" size="small"
                        no-filtered-data-text="暂无筛选结果">
                        <template slot-scope="{ row, index }" slot="action">
                            <a @click="Info(row)">详情</a>
                        </template>
                    </Table>
                <!-- 会员详情-->
                <data-details ref="dataDetails" ></data-details>
                </Card>
            </TabPane>
            <TabPane label="备份列表" name="name2">
                <Card :bordered="false" dis-hover >
                <Table ref="selection" :columns="columns4" :data="tabList" :loading="loading3"
                    no-data-text="暂无数据" highlight-row size="small"
                    no-filtered-data-text="暂无筛选结果">
                    <template slot-scope="{ row, index }" slot="action">
                        <a @click="ImportFile(row)">导入</a>
                        <Divider type="vertical"/>
                        <a @click="del(row,'删除该备份',index)">删除</a>
                        <Divider type="vertical"/>
                        <a @click="download(row)">下载</a>
                    </template>
                </Table>
                </Card>
            </TabPane>
        
        </Tabs>
      </div>
    </Card>
    </div>
</template>

<script>
    import { backupListApi, backupBackupApi, backupOptimizeApi, backupRepairApi, filesListApi, filesDownloadApi, filesImportApi } from '@/api/system';
    import Setting from '@/setting';
    import dataDetails from "./handle/dataDetails";
    export default {
        name: 'systemDatabackup',
        components: {
            dataDetails,
        },
        data () {
            return {
                modals: false,
                loading: false,
                tabList: [],
                columns4: [
                    {
                        title: '备份名称',
                        key: 'filename',
                        minWidth: 200,
                        sortable: true
                    },
                    {
                        title: 'part',
                        key: 'part',
                        minWidth: 100
                    },
                    {
                        title: '大小',
                        key: 'size',
                        minWidth: 150
                    },
                    {
                        title: 'compress',
                        key: 'compress',
                        minWidth: 100
                    },
                    {
                        title: '时间',
                        key: 'backtime',
                        minWidth: 150
                    },
                    {
                        title: '操作',
                        slot: 'action',
                        fixed: 'right',
                        minWidth: 100
                    }
                ],
                tabList2: [],
                columns: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: '表名称',
                        key: 'name',
                        minWidth: 200,
                        sortable: true
                    },
                    {
                        title: '备注',
                        key: 'comment',
                        minWidth: 200
                    },
                    {
                        title: '类型',
                        key: 'engine',
                        minWidth: 130,
                        sortable: true
                    },
                    {
                        title: '大小',
                        key: 'data_length',
                        minWidth: 130,
                        sortable: true
                    },
                    {
                        title: '更新时间',
                        key: 'update_time',
                        minWidth: 150,
                        sortable: true
                    },
                    {
                        title: '行数',
                        key: 'rows',
                        minWidth: 100,
                        sortable: true
                    },
                    {
                        title: '操作',
                        slot: 'action',
                        fixed: 'right',
                        minWidth: 60
                    }
                ],
                selectionList: [],
                rows: {},
                dataList: {},
                loading2: false,
                loading3: false,
                header: {},
                Token: ''
            }
        },
        computed: {
            fileUrl () {
                const search = '/adminapi/';
                const start = Setting.apiBaseURL.indexOf(search);
                return Setting.apiBaseURL.substring(0, start);// 截取字符串
            }
        },
        created () {
            this.getToken();
            this.getList();
            this.getfileList();
        },
        methods: {
            // 导入
            ImportFile (row) {
                filesImportApi({
                    part: row.part,
                    time: row.time
                }).then(async res => {
                    this.$Message.success(res.msg);
                    this.getfileList();
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                })
            },
            // 删除备份记录表
            del (row, tit, num) {
                let delfromData = {
                    title: tit,
                    num: num,
                    url: `system/backup/del_file`,
                    method: 'DELETE',
                    ids: {
                        filename: row.time
                    }
                };
                this.$modalSure(delfromData).then((res) => {
                    this.$Message.success(res.msg);
                    this.tabList.splice(num, 1);
                }).catch(res => {
                    this.$Message.error(res.msg);
                });
            },
            // 上传头部token
            getToken () {
                this.$store.dispatch('admin/db/database', {
                    user: true
                }).then(res => {
                    this.Token = res.get('TOKEN').value();
                })
            },
            download (row) {
                let data = {
                    time: row.time
                }
                filesDownloadApi(data).then(res => {
                    if (res.data.key) {
                       window.open(Setting.apiBaseURL + '/download?key=' + res.data.key)
                    }
                }).catch(res => {
                    this.$Message.error(res);
                })
            },
            // 导出备份记录表
            exportData () {
                const columns = this.columns.slice(1, 7);
                this.$refs.selection.exportCsv({
                    filename: '导出',
                    columns: columns,
                    data: this.tabList2
                });
            },
            // 全选
            onSelectTab (selection) {
                this.selectionList = selection;
                let tables = [];
                this.selectionList.map((item) => {
                    tables.push(item.name)
                });
                this.dataList = {
                    tables: tables.join(',')
                };
            },
            // 备份表
            getBackup () {
                if (this.selectionList.length === 0) {
                    return this.$Message.warning('请选择表');
                }
                backupBackupApi(this.dataList).then(async res => {
                    this.$Message.success(res.msg);
                    this.getfileList();
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                })
            },
            // 备份记录表列表
            getfileList () {
                this.loading3 = true;
                filesListApi().then(async res => {
                    let data = res.data;
                    this.tabList = data.list;
                    this.loading3 = false;
                }).catch(res => {
                    this.loading3 = false;
                    this.$Message.error(res.msg);
                })
            },
            // 优化表
            getOptimize () {
                if (this.selectionList.length === 0) {
                    return this.$Message.warning('请选择表');
                }
                backupOptimizeApi(this.dataList).then(async res => {
                    this.$Message.success(res.msg);
                }).catch(res => {
                    this.$Message.error(res.msg);
                })
            },
            // 修复表
            getRepair () {
                if (this.selectionList.length === 0) {
                    return this.$Message.warning('请选择表');
                }
                backupRepairApi(this.dataList).then(async res => {
                    this.$Message.success(res.msg);
                }).catch(res => {
                    this.$Message.error(res.msg);
                })
            },
            // 数据库列表
            getList () {
                this.loading = true;
                backupListApi().then(async res => {
                    let data = res.data;
                    this.tabList2 = data.list;
                    this.loading = false;
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                })
            },
            // 详情
            Info (row) {
                this.$refs.dataDetails.modals = true;
                this.$refs.dataDetails.activeName = "datainfo";
                this.$refs.dataDetails.getDetails(row);
            }
        }
    }
</script>

<style scoped lang="stylus">
    .tableBox >>> .ivu-table-header table
       border none !important
    .new_tab {
        >>>.ivu-tabs-nav .ivu-tabs-tab{
            padding:4px 16px 20px !important;
            font-weight: 500;
        }
    }
</style>
