<template>
  <Modal
    v-model="modals"
    scrollable
    title="退款处理"
    class="order_box"
    :closable="false"
  >
    <Form
      ref="formValidate"
      :model="formValidate"
      :rules="ruleValidate"
      :label-width="100"
      @submit.native.prevent
    >
	  <FormItem label="退款单号：" prop="order_id">
	    <Input
	      v-model="formValidate.order_id"
	      placeholder="退款单号"
	      style="width: 100%"
		  disabled
	    />
	  </FormItem>
	  <FormItem label="退款金额：" prop="refund_price">
	    <InputNumber
	      v-model="formValidate.refund_price"
	    />
		<span class="red">（包含邮费：{{formValidate.pay_postage || 0}}）</span>
	  </FormItem>
    </Form>
    <div slot="footer">
      <Button @click="cancel('formValidate')">取消</Button>
	  <Button type="primary" @click="putRemark('formValidate')">提交</Button>
    </div>
  </Modal>
</template>

<script>
import { putRefundOrderFrom } from "@/api/order";
export default {
  name: "refundFrom",
  data() {
    return {
      formValidate: {
        order_id: '',
		refund_price:0,
		pay_postage:'',
		id:0
      },
      modals: false,
      ruleValidate: {
        refund_price: [
          { required: true, type: 'number', message: "请输入退款金额", trigger: "blur" }
        ],
      },
    };
  },
  methods: {
    cancel(name) {
      this.modals = false;
      this.$refs[name].resetFields();
    },
    putRemark(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          putRefundOrderFrom(this.formValidate)
            .then(async (res) => {
              this.$Message.success(res.msg);
              this.modals = false;
              this.$refs[name].resetFields();
              this.$emit("submitSuccess");
            })
            .catch((res) => {
              this.$Message.error(res.msg);
            });
        } else {
          this.$Message.warning("请输入退款金额");
        }
      });
    },
  },
};
</script>

<style scoped>
</style>