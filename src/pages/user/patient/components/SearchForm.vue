<template>
  <Card :bordered="false" dis-hover class="ivu-mt" :padding="0">
    <div class="padding-add">
      <Form
        ref="searchForm"
        :model="formData"
        :label-width="100"
        label-position="left"
        @submit.native.prevent
      >
        <Row :gutter="24">
          <Col span="18">
            <Row :gutter="16">
              <Col span="6">
                <FormItem label="就诊人ID：">
                  <Input
                    v-model="formData.card_no"
                    placeholder="请输入就诊人ID"
                    clearable
                  />
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="身份证号：">
                  <Input
                    v-model="formData.id_card"
                    placeholder="请输入身份证号"
                    clearable
                  />
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="手机号：">
                  <Input
                    v-model="formData.phone_number"
                    placeholder="请输入手机号"
                    clearable
                  />
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="就诊人：">
                  <Input
                    v-model="formData.patient_name"
                    placeholder="请输入就诊人"
                    clearable
                  />
                </FormItem>
              </Col>
            </Row>
          </Col>
          <Col span="6" class="ivu-text-right">
            <FormItem>
              <Button
                type="primary"
                :loading="loading"
                @click="handleSearch"
              >
                搜索
              </Button>
              <Button 
                class="ml10"
                @click="handleReset"
              >
                重置
              </Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>
  </Card>
</template>

<script>
export default {
  name: 'SearchForm',
  props: {
    formData: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleSearch() {
      this.$emit('search', this.formData)
    },
    handleReset() {
      // 重置表单数据
      Object.keys(this.formData).forEach(key => {
        if (key !== 'page' && key !== 'limit') {
          this.formData[key] = ''
        }
      })
      this.formData.page = 1
      this.$emit('reset', this.formData)
    }
  }
}
</script>

<style scoped>
.padding-add {
  padding: 20px;
}
.ml10 {
  margin-left: 10px;
}
</style>
