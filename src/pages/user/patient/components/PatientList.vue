<template>
  <Card :bordered="false" dis-hover class="ivu-mt">
    <!-- 标签页 -->
    <div class="acea-row row-between-wrapper">
      <div class="acea-row">
        <div
          v-for="item in headerTabs"
          :key="item.type"
          :class="['header-item', { active: activeTab === item.type }]"
          @click="handleTabChange(item.type)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <Table
      ref="table"
      :data="data"
      :columns="columns"
      :loading="loading"
      stripe
      @on-selection-change="onSelectionChange"
    />

    <!-- 分页 -->
    <div class="acea-row row-right page">
      <Page
        :current="currentPage"
        :total="total"
        :page-size="pageSize"
        show-elevator
        show-sizer
        show-total
        @on-change="handlePageChange"
        @on-page-size-change="handlePageSizeChange"
      />
    </div>
  </Card>
</template>

<script>
export default {
  name: 'PatientList',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    total: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 20
    },
    headerTabs: {
      type: Array,
      default: () => []
    },
    activeTab: {
      type: String,
      default: '-1'
    }
  },
  data() {
    return {
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '就诊人ID',
          key: 'card_no',
          width: 120
        },
        {
          title: '就诊人',
          key: 'patient_name',
          width: 100
        },
        {
          title: '性别',
          key: 'gender',
          width: 80,
          render: (h, params) => {
            return h('span', params.row.gender === 1 ? '男' : '女')
          }
        },
        {
          title: '年龄',
          key: 'age',
          width: 80
        },
        {
          title: '身份证号',
          key: 'id_card',
          width: 180
        },
        {
          title: '手机号',
          key: 'phone_number',
          width: 120
        },
        {
          title: '创建时间',
          key: 'created_at',
          width: 160
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          fixed: 'right',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.handleViewDetails(params.row)
                  }
                }
              }, '查看详情'),
              h('Button', {
                props: {
                  type: 'warning',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleEdit(params.row)
                  }
                }
              }, '编辑')
            ])
          }
        }
      ]
    }
  },
  methods: {
    handleTabChange(type) {
      this.$emit('tab-change', type)
    },
    handlePageChange(page) {
      this.$emit('page-change', page)
    },
    handlePageSizeChange(pageSize) {
      this.$emit('page-size-change', pageSize)
    },
    handleViewDetails(row) {
      this.$emit('view-details', row)
    },
    handleEdit(row) {
      this.$emit('edit', row)
    },
    onSelectionChange(selection) {
      this.$emit('selection-change', selection)
    }
  }
}
</script>

<style scoped>
.header-item {
  padding: 10px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.header-item.active {
  color: #2d8cf0;
  border-bottom-color: #2d8cf0;
}

.header-item:hover {
  color: #2d8cf0;
}

.page {
  margin-top: 20px;
  padding: 0 20px 20px;
}
</style>
