<template>
    <Drawer :closable="false"
            width="1000"
            class-name="order_box"
            v-model="modals"
            :styles="{ padding: 0 }">
        <div class="acea-row user-row">
            <div class="avatar mr15">
                <img :src="psInfo.avatar">
            </div>
            <div class="user-row-text">
                <div>
                    <span class="nickname">{{ psInfo.nickname || '-' }}{{psInfo.delete_time != null?' (已注销)':''}}</span>
                    <i :class="{
                        iconxiaochengxu: psInfo.user_type === 'routine',
                        icongongzhonghao: psInfo.user_type === 'wechat',
                        iconPC: psInfo.user_type === 'pc',
                        iconh5: psInfo.user_type === 'h5',
                        iconapp: psInfo.user_type === 'app'
                      }"
                       class="iconfont"></i>
                </div>
                <div class="level">
                    <img v-if="psInfo.is_money_level"
                         src="@/assets/images/svip-user.png">
                    <span v-if="psInfo.level"
                          class="vip">V{{ psInfo.level }}</span>
                </div>
            </div>
            <div class="user-row-action"
                 v-if="fromType !== 'order' && psInfo.delete_time == null ">
                <Button v-show="isEdit"
                        @click="isEdit = false">取消</Button>
                <Button v-show="isEdit"
                        type="primary"
                        @click="finish">完成</Button>
                <Button v-show="!isEdit && activeName === 'info'"
                        type="primary"
                        @click="isEdit = true">编辑</Button>
                <Button type="success"
                        @click="changeMenu('2')">积分余额</Button>
                <Button @click="changeMenu('3')">赠送会员</Button>
            </div>
        </div>
        <div class="acea-row info-row">
            <div v-for="(item, index) in detailsData"
                 :key="index"
                 class="info-row-item">
                <div class="info-row-item-title">{{ item.title }}</div>
                <div>{{ item.value }}{{ item.key }}</div>
            </div>
        </div>
        <Tabs v-model="activeName">
            <TabPane v-for="(item, index) in list"
                     :key="index"
                     :label="item.label"
                     :name="item.val">
                <template v-if="item.val === 'info'">
                    <user-form v-if="isEdit"
                               ref="userForm"
                               :ps-info="psInfo"
                               @change-menu="changeMenu"></user-form>
                    <user-info v-else
                               :ps-info="psInfo"
                               :workMemberInfo="workMemberInfo"
                               :workClientInfo="workClientInfo"></user-info>
                </template>
                <template v-else>
                    <Table :columns="columns"
                           :data="userLists"
                           ref="table"
                           :loading="loading"
                           no-userFrom-text="暂无数据"
                           no-filtered-userFrom-text="暂无筛选结果">
                        <template slot-scope="{ row }"
                                  slot="coupon_price">
                            <span v-if="row.coupon_type==1">{{row.coupon_price}}元</span>
                            <span v-if="row.coupon_type==2">{{parseFloat(row.coupon_price)/10}}折（{{row.coupon_price.toString().split(".")[0]}}%）</span>
                        </template>
                        <template slot-scope="{ row }"
                                  slot="product">
                            <div class="product">
                                <div class="image"
                                     v-viewer>
                                    <img v-lazy="row.image">
                                </div>
                                <div class="title">{{ row.store_name }}</div>
                            </div>
                        </template>
                    </Table>
                    <div class="acea-row row-right page">
                        <Page :total="total"
                              :current.sync="userFrom.page"
                              show-elevator
                              show-total
                              @on-change="pageChange"
                              :page-size="userFrom.limit" />
                    </div>
                </template>
            </TabPane>
        </Tabs>
        <Modal v-model="reportDetailModal"
               title="舌诊报告详情"
               :width="450"
               :footer-hide="true"
               :mask-closable="false">
            <div v-if="reportDetail">
                <div class="container">
                    <div class="report-card">
                        <div class="report-cards"
                             :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/gback.png)' }">
                            <div class="report-title">
                                <div class="report-text">{{physique_name }}</div>
                            </div>
                            <div class="report-subtitle">
                                <div class="subtitle-text">{{syndrome_name }}</div>
                            </div>
                            <div class="score-container">
                                <canvas class="gauge-canvas"
                                        id="gauge"></canvas>
                            </div>
                            <div style="float: right; margin-right: 20px">
                                <img :src="'https://67686161.com' + '/statics/images/product/xlog.png'"
                                     style="width: 100px; height: 100px" />
                            </div>
                        </div>
                        <div class="report-content">
                            <div class="section-title">
                                <img class="set_icon"
                                     style="width: 10px; height: 10px; margin-right: 10px;"
                                     :src="'https://67686161.com' + '/statics/images/product/titles.png'" />
                                <div class="section-text">体质辨识</div>
                            </div>
                            <div class="report-details">
                                <div class="detail-text">{{syndrome_introduction }}</div>
                            </div>
                        </div>
                        <div class="body-analysis">
                            <div class="analysis-title">
                                <div class="analysis-text">体质分析</div>
                            </div>
                            <div class="body-outline"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/people.png)' }">
                                <div class="body-icon"></div>
                                <div class="body-labels">
                                    <!-- 左边标签 -->
                                    <div class="label"
                                         v-for="(answer, index) in leftAnswers"
                                         :key="`left-${index}`"
                                         :style="{ top: 3 + index * 15 + '%', left: '5%' }">
                                        {{ answer.name }}
                                    </div>
                                    <!-- 右边标签 -->
                                    <div class="label"
                                         v-for="(answer, index) in rightAnswers"
                                         :key="`right-${index}`"
                                         :style="{ top: 3 + index * 15 + '%', left: '75%' }">
                                        {{ answer.name }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 冠心病 -->
                        <div class="disease-section">
                            <div class="disease-details">
                                <div class="disease-text">{{physique_analysis }}</div>
                            </div>
                        </div>

                        <div class="report-card"
                             style="margin-top: 20px;">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">典型特征</div>
                                </div>
                            </div>
                            <!-- 典型特征 -->
                            <div class="disease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{typical_symptom}}</div>
                                </div>
                            </div>
                        </div>

                        <div class="report-card"
                             style="margin-top: 20px;">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">风险预警</div>
                                </div>
                            </div>
                            <!-- 风险预警 -->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{risk_warning}}</div>
                                </div>
                            </div>
                        </div>
                        <!-- 舌面识别 -->
                        <div class="report-card"
                             style="margin-top: 20px;">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">舌面识别</div>
                                </div>
                            </div>
                            <div class="disease-sectionAAA">
                                <div class="image-container">
                                    <img class="tongue-image"
                                         :src="'https://67686161.com'+'/statics/images/product/bj.png'"></img>
                                    <div class="background"
                                         :style="{ backgroundImage: 'url(' + surl + ')' }"></div>
                                </div>
                                <div v-for="(item,index) in shefeatures"
                                     style="margin-top: 20px;margin-bottom: 20px;">
                                    <div class="section-titleBBB">
                                        <img class="set_icon"
                                             style="width: 10px;height: 10px;margin-right: 10px;"
                                             :src="'https://67686161.com'+'/statics/images/product/titles.png'"></img>
                                        <div class="section-text">{{item.feature_group}}</div>
                                    </div>
                                    <div class="section-title">
                                        <div class="section-text"
                                             style="margin-left: 40px;">{{item.feature_name}}</div>
                                    </div>
                                    <div class="disease-details">
                                        <div class="disease-text">{{item.feature_interpret}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--面部识别-->
                        <div class="report-card"
                             style="margin-top: 20px;">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">面部识别</div>
                                </div>
                            </div>
                            <div class="disease-sectionAAA">
                                <div class="image-container">
                                    <img class="tongue-image"
                                         :src="'https://67686161.com'+'/statics/images/product/bj.png'"></img>
                                    <div class="background"
                                         :style="{ backgroundImage: 'url(' + ff_image + ')' }"></div>
                                </div>
                                <div v-for="(item,index) in featuresface"
                                     style="margin-top: 20px;margin-bottom: 20px;">
                                    <div class="section-titleBBB">
                                        <img class="set_icon"
                                             style="width: 10px;height: 10px;margin-right: 10px;"
                                             :src="'https://67686161.com'+'/statics/images/product/titles.png'"></img>
                                        <div class="section-text">{{item.feature_group}}</div>
                                    </div>
                                    <div class="section-title">
                                        <div class="section-text"
                                             style="margin-left: 40px;">{{item.feature_name}}</div>
                                    </div>
                                    <div class="disease-details">
                                        <div class="disease-text">{{item.feature_interpret}}</div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <!-- 运动建议 -->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item,index) in sport"
                             :key="`sport-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!-- 运动建议 -->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 饮食建议 -->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item,index) in food"
                             :key="`food-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!-- 饮食建议 -->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 外治建议 -->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item, index) in treatment"
                             :key="`treatment-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!-- 外治建议 -->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                        <!--音乐建议-->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item,index) in music"
                             :key="`music-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!--音乐建议-->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                        <!--起居建议-->
                        <div class="report-card"
                             style="margin-top: 20px;"
                             v-for="(item,index) in sleep"
                             :key="`sleep-${index}`">
                            <div class="body-analysisS"
                                 :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px'}">
                                <div class="analysis-titles">
                                    <div class="analysis-texts">{{item.title}}</div>
                                </div>
                            </div>
                            <!--起居建议-->
                            <div class="Adisease-section">
                                <div class="disease-details">
                                    <div class="disease-text">{{item.advice}}</div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </Modal>
    </Drawer>
</template>

<script>
    import { detailsApi, infoApi, visitList, spreadList, getreportdetail } from '@/api/user';
    import userForm from './userForm';
    import userInfo from './userInfo';

    export default {
        name: 'userDetails',
        components: {
            userForm,
            userInfo
        },
        props: ['levelList', 'labelList', 'groupList', 'fromType'],
        data () {
            return {
                theme2: 'light',
                list: [
                    { val: 'info', label: '用户信息' },
                    { val: 'order', label: '消费记录' },
                    { val: 'integral', label: '积分明细' },
                    { val: 'sign', label: '签到记录' },
                    { val: 'coupon', label: '持有优惠券' },
                    { val: 'balance_change', label: '余额变动' },
                    { val: 'spread', label: '好友关系' },
                    { val: 'visit', label: '浏览足迹' },
                    { val: 'spread_change', label: '推荐人变更记录' },
                    { val: 'belong_store_change', label: '归属门店变更记录' },
                    { val: 'salesman_change', label: '绑定店员变更记录' },
                    { val: 'tongue_diagnosis_report', label: '舌诊报告' }
                ],
                modals: false,
                spinShow: false,
                detailsData: [],
                userId: 0,
                loading: false,
                userFrom: {
                    type: 'info',
                    page: 1, // 当前页
                    limit: 12 // 每页显示条数
                },
                total: 0,
                columns: [],
                userLists: [],
                psInfo: {},
                workMemberInfo: {},
                workClientInfo: {},
                activeName: 'info',
                isEdit: false,
                groupOptions: [],
                labelOptions: [],
                reportDetailModal: false, // 舌诊报告详情弹窗状态
                reportDetail: null, // 舌诊报告详情数据
                score: '', // 示例分数
                resData: [],
                physique_name: '',
                physique_analysis: '',
                syndrome_introduction: '',
                syndrome_name: '',
                answers: [],
                food: [],
                music: [],
                sport: [],
                sleep: [],
                treatment: [],
                surl: '',
                ff_image: '',
                risk_warning: '',
                featuresface: [],
                shefeatures: [],
                typical_symptom: '',
                typical_symptom_arr: [],
                colors: ['#4a90e2', '#ff7e5f', '#50e3c2', '#f6b93b', '#9e9e9e'],
                fontSizes: [12, 14, 16, 18, 20],
                detailId: ''
            }
        },
        computed: {
            // 根据 key 的奇偶性将 answers 分为左边和右边
            leftAnswers () {
                return this.answers.filter((answer, index) => index % 2 === 0);
            },
            rightAnswers () {
                return this.answers.filter((answer, index) => index % 2 === 1);
            },
        },
        watch: {
            activeName (value) {
                this.userFrom.page = 1;
                if (value == 'info') return;
                this.isEdit = false;
                this.changeType(value);
            },
            modals (value) {
                if (!value) {
                    this.isEdit = false;
                    this.activeName = 'info';
                    this.reportDetailModal = false; // 关闭主弹窗时也关闭舌诊报告详情弹窗
                }
            },
            reportDetailModal (value) {
                if (!value) {
                    // 弹窗关闭时清除 canvas
                    const canvas = document.getElementById('gauge');
                    if (canvas) {
                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                    }
                }
            }
        },
        created () { },
        methods: {
            showDetailDialog (id) {
                console.log(id, '8888')
                this.modals = true;
                this.userId = id;
                this.getDetails(id);
            },
            //获取详情数据
            detail (report) {
                console.log(report, '大哥哥')
                getreportdetail({ id: report }).then(res => {
                    // 打印传递的 resData
                    this.resData = res.data; // 将数据赋值给页面的 data
                    console.log(res.data, res.data.score, '罗鑫')
                    //分数
                    this.score = res.data.score
                    //体质名称
                    this.physique_name = this.resData.physique_name
                    //体质辨识
                    this.syndrome_introduction = this.resData.syndrome_introduction
                    this.syndrome_name = this.resData.syndrome_name
                    //获取选中答题是
                    this.answers = this.resData.answers

                    //饮食建议
                    this.food = this.resData.advices.food
                    //音乐建议
                    this.music = this.resData.advices.music
                    //运动建议
                    this.sport = this.resData.advices.sport
                    //起居建议
                    this.sleep = this.resData.advices.sleep
                    //外治建议
                    this.treatment = this.resData.advices.treatment
                    //舌头
                    this.surl = this.resData.tf_detect_matches.url
                    this.physique_analysis = this.resData.physique_analysis
                    this.risk_warning = this.resData.risk_warning
                    //面部
                    this.featuresface = this.resData.features_arr[0]
                    //舌部
                    this.shefeatures = this.resData.features_arr[1]
                    this.typical_symptom = this.resData.typical_symptom
                    this.typical_symptom_arr = this.resData.typical_symptom_arr || []; // 确保赋值
                    this.ff_image = this.resData.ff_image
                    // 确保 canvas 元素已渲染后再绘制
                    this.$nextTick(() => {
                        this.drawGauge();
                    });
                })
            },



            drawGauge () {
                // 获取 canvas 元素
                const canvas = document.getElementById('gauge');
                if (!canvas) {
                    console.error('Canvas element not found');
                    return;
                }

                // 获取 canvas 上下文
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    console.error('Canvas context not found');
                    return;
                }
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = Math.min(centerX, centerY) - 10;
                const startAngle = Math.PI + Math.PI * 0.005; // 调整起始角度，闭合90%
                const endAngle = Math.PI * 2 - Math.PI * 0.005; // 调整结束角度
                const maxScore = 100;
                const minScore = 50;
                const percentage = (this.score - minScore) / (maxScore - minScore);
                const sweepAngle = (endAngle - startAngle) * percentage;

                // 绘制背景圆弧
                const ticks = [50, 60, 70, 80, 90, 100];
                const colors = ['#FE8B86', '#FEB936', '#9EE202', '#24ED05', '#21E303']; // 颜色数组
                for (let i = 0; i < ticks.length - 1; i++) {
                    const tickPercentage = (ticks[i] - minScore) / (maxScore - minScore);
                    const nextTickPercentage = (ticks[i + 1] - minScore) / (maxScore - minScore);
                    const tickAngle = startAngle + tickPercentage * (endAngle - startAngle);
                    const nextTickAngle = startAngle + nextTickPercentage * (endAngle - startAngle);

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, tickAngle, nextTickAngle);
                    ctx.strokeStyle = colors[i];
                    ctx.lineWidth = 10;
                    ctx.stroke();
                }

                // 绘制刻度值（放在圆圈里面）
                for (let i = 0; i < ticks.length; i++) {
                    const tickPercentage = (ticks[i] - minScore) / (maxScore - minScore);
                    const tickAngle = startAngle + tickPercentage * (endAngle - startAngle);

                    // 绘制刻度值
                    ctx.font = '12px Arial';
                    ctx.fillStyle = '#fff'; // 设置数字颜色为白色
                    ctx.textAlign = 'center';
                    ctx.fillText(
                        ticks[i].toString(),
                        centerX + radius * 0.7 * Math.cos(tickAngle), // 调整位置，放在圆圈里面
                        centerY + radius * 0.7 * Math.sin(tickAngle) + 5
                    );
                }

                // 绘制指针（缩短指针长度）
                ctx.beginPath();
                ctx.moveTo(centerX, centerY); // 指针起始点
                ctx.lineTo(
                    centerX + radius * 0.6 * Math.cos(startAngle + sweepAngle), // 指针终点（缩短长度）
                    centerY + radius * 0.6 * Math.sin(startAngle + sweepAngle)
                );
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 3;
                ctx.stroke();

                // 绘制中心点
                ctx.beginPath();
                ctx.arc(centerX, centerY, 5, 0, Math.PI * 2);
                ctx.fillStyle = '#fff';
                ctx.fill();

                // 绘制分数
                ctx.font = '24px Arial';
                ctx.fillStyle = '#fff';
                ctx.textAlign = 'center';
                ctx.fillText(this.score.toString(), centerX, centerY + 30); // 调整分数位置
            },
            updateScore (newScore) {
                this.score = newScore;
                this.drawGauge();
            },
            changeMenu (value) {
                if (value === '99') {
                    this.getDetails(this.userId);
                    this.$parent.getList();
                    this.isEdit = false;
                    return;
                }
                this.$parent.changeMenu(this.psInfo, value);
            },
            // 完成
            finish () {
                this.$refs.userForm[0].detailsPut();
            },
            // 推荐人变更记录
            getSpreadList () {
                this.loading = true;
                spreadList({
                    id: this.userId,
                    datas: {
                        page: this.userFrom.page,
                        limit: this.userFrom.limit
                    }
                }).then(async res => {
                    if (res.status === 200) {
                        let data = res.data;
                        this.userLists = data.list;
                        this.total = data.count;
                        this.columns = [
                            {
                                title: '推荐人ID',
                                key: 'spread_uid',
                                minWidth: 120
                            },
                            {
                                title: '推荐人',
                                key: 'nickname',
                                minWidth: 120,
                                render: (h, params) => {
                                    return h('div', [
                                        h('img', {
                                            style: {
                                                borderRadius: '50%',
                                                marginRight: '10px',
                                                verticalAlign: 'middle'
                                            },
                                            attrs: {
                                                with: 38,
                                                height: 38
                                            },
                                            directives: [
                                                {
                                                    name: 'lazy',
                                                    value: params.row.avatar
                                                },
                                                {
                                                    name: 'viewer'
                                                }
                                            ]
                                        }),
                                        h('span', {
                                            style: {
                                                verticalAlign: 'middle'
                                            }
                                        }, params.row.nickname)
                                    ]);
                                }
                            },
                            {
                                title: '变更方式',
                                key: 'type',
                                minWidth: 120
                            },
                            {
                                title: '变更时间',
                                key: 'spread_time',
                                minWidth: 120
                            }
                        ];
                        this.loading = false;
                    } else {
                        this.loading = false;
                        this.$Message.error(res.msg);
                    }
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                });
            },
            // 浏览足迹
            getVisitList () {
                this.loading = true;
                visitList({
                    id: this.userId,
                    datas: {
                        page: this.userFrom.page,
                        limit: this.userFrom.limit
                    }
                }).then(async res => {
                    if (res.status === 200) {
                        let data = res.data;
                        this.userLists = data.list;
                        this.total = data.count;
                        this.columns = [
                            {
                                title: '商品信息',
                                slot: 'product',
                                minWidth: 400
                            },
                            {
                                title: '价格',
                                key: 'product_price',
                                minWidth: 120,
                                render: (h, params) => {
                                    return h('div', `¥${params.row.product_price}`);
                                }
                            },
                            {
                                title: '浏览时间',
                                key: 'add_time',
                                minWidth: 120
                            }
                        ];
                        this.loading = false;
                    } else {
                        this.loading = false;
                        this.$Message.error(res.msg);
                    }
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                });
            },
            // 会员详情
            getDetails (id) {
                this.userId = id;
                this.spinShow = true;
                detailsApi(id).then(async res => {
                    if (res.status === 200) {
                        let data = res.data
                        this.detailsData = data.headerList;
                        if (this.fromType !== 'order') {
                            let groupItem = this.groupList.find(item => item.id == data.ps_info.group_id);
                            if (groupItem) {
                                data.ps_info.group_name = groupItem.group_name;
                            }
                        }
                        this.psInfo = data.ps_info;
                        this.workMemberInfo = data.workMemberInfo;
                        this.workClientInfo = data.workClientInfo;
                        this.spinShow = false;
                    } else {
                        this.spinShow = false;
                        this.$Message.error(res.msg);
                    }
                }).catch(res => {
                    this.spinShow = false;
                    this.$Message.error(res.msg);
                })
            },
            pageChange (index) {
                this.userFrom.page = index
                this.changeType(this.userFrom.type);
            },
            // tab选项
            changeType (name) {
                this.loading = true;
                this.userFrom.type = name;
                this.activeName = name;
                let data = {
                    id: this.userId,
                    datas: this.userFrom
                }
                infoApi(data).then(async res => {
                    if (res.status === 200) {
                        let data = res.data
                        this.userLists = data.list;
                        this.total = data.count;
                        switch (this.userFrom.type) {
                            case 'order':
                                this.columns = [
                                    {
                                        title: '订单ID',
                                        key: 'order_id',
                                        minWidth: 160
                                    },
                                    {
                                        title: '收货人',
                                        key: 'real_name',
                                        minWidth: 100
                                    },
                                    {
                                        title: '商品数量',
                                        key: 'total_num',
                                        minWidth: 90
                                    },
                                    {
                                        title: '商品总价',
                                        key: 'total_price',
                                        minWidth: 110
                                    },
                                    {
                                        title: '实付金额',
                                        key: 'pay_price',
                                        minWidth: 120
                                    },
                                    {
                                        title: '交易完成时间',
                                        key: 'pay_time',
                                        minWidth: 120
                                    }
                                ]
                                break;
                            case 'integral':
                                this.columns = [
                                    {
                                        title: '来源/用途',
                                        key: 'title',
                                        minWidth: 120
                                    },
                                    {
                                        title: '积分变化',
                                        key: 'number',
                                        minWidth: 120
                                    },
                                    {
                                        title: '变化后积分',
                                        key: 'balance',
                                        minWidth: 120
                                    },
                                    {
                                        title: '日期',
                                        key: 'add_time',
                                        minWidth: 120
                                    },
                                    {
                                        title: '备注',
                                        key: 'mark',
                                        minWidth: 120
                                    }
                                ]
                                break;
                            case 'sign':
                                this.columns = [
                                    {
                                        title: '获得积分',
                                        key: 'number',
                                        minWidth: 120
                                    },
                                    {
                                        title: '签到时间',
                                        key: 'add_time',
                                        minWidth: 120
                                    },
                                    {
                                        title: '备注',
                                        key: 'mark',
                                        minWidth: 120
                                    }
                                ]
                                break;
                            case 'coupon':
                                this.columns = [
                                    {
                                        title: '优惠券名称',
                                        key: 'coupon_title',
                                        minWidth: 120
                                    },
                                    {
                                        title: '面值',
                                        slot: 'coupon_price',
                                        minWidth: 120
                                    },
                                    {
                                        title: '有效期(天)',
                                        key: 'coupon_time',
                                        minWidth: 120
                                    },
                                    {
                                        title: '兑换时间',
                                        key: '_add_time',
                                        minWidth: 120
                                    }
                                ]
                                break;
                            case 'balance_change':
                                this.columns = [
                                    {
                                        title: '动作',
                                        key: 'title',
                                        minWidth: 120
                                    },
                                    {
                                        title: '变动金额',
                                        key: 'number',
                                        minWidth: 120
                                    },
                                    {
                                        title: '变动后',
                                        key: 'balance',
                                        minWidth: 120
                                    },
                                    {
                                        title: '创建时间',
                                        key: 'add_time',
                                        minWidth: 120
                                    },
                                    {
                                        title: '备注',
                                        key: 'mark',
                                        minWidth: 120
                                    }
                                ]
                                break;
                            case 'visit':
                                this.columns = [
                                    {
                                        title: '商品信息',
                                        slot: 'product',
                                        minWidth: 400,
                                    },
                                    {
                                        title: '价格',
                                        key: 'product_price',
                                        minWidth: 120,
                                        render: (h, params) => {
                                            return h('div', `¥${params.row.product_price}`)
                                        },
                                    },
                                    {
                                        title: '浏览时间',
                                        key: 'add_time',
                                        minWidth: 120,
                                    },
                                ]
                                break
                            case 'spread_change':
                                this.columns = [
                                    {
                                        title: '推荐人ID',
                                        key: 'spread_uid',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '推荐人',
                                        key: 'nickname',
                                        minWidth: 120,
                                        render: (h, params) => {
                                            return h('div', [
                                                h('img', {
                                                    style: {
                                                        borderRadius: '50%',
                                                        marginRight: '10px',
                                                        verticalAlign: 'middle',
                                                    },
                                                    attrs: {
                                                        with: 38,
                                                        height: 38,
                                                    },
                                                    directives: [
                                                        {
                                                            name: 'lazy',
                                                            value: params.row.avatar,
                                                        },
                                                        {
                                                            name: 'viewer',
                                                        },
                                                    ],
                                                }),
                                                h(
                                                    'span',
                                                    {
                                                        style: {
                                                            verticalAlign: 'middle',
                                                        },
                                                    },
                                                    params.row.nickname
                                                ),
                                            ])
                                        },
                                    },
                                    {
                                        title: '变更方式',
                                        key: 'type',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '变更时间',
                                        key: 'spread_time',
                                        minWidth: 120,
                                    },
                                ]
                                break;
                            case 'belong_store_change':
                                this.columns = [
                                    {
                                        title: '归属门店',
                                        key: 'store_name',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '变更类型',
                                        key: 'group_name',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '变更事件',
                                        key: 'type_name',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '变更时间',
                                        key: 'add_time',
                                        minWidth: 120,
                                    },
                                ]
                                break;
                            case 'tongue_diagnosis_report':
                                this.columns = [
                                    {
                                        title: '体质名称',
                                        key: 'physique_name',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '分数',
                                        key: 'score',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '创建时间',
                                        key: 'add_time',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '操作',
                                        slot: 'action',
                                        minWidth: 120,
                                        render: (h, params) => {
                                            return h('Button', {
                                                props: {
                                                    type: 'text',
                                                    size: 'small'
                                                },
                                                on: {
                                                    click: () => {
                                                        this.showReportDetailDialog(params.row.id);
                                                    }
                                                }
                                            }, '详情');
                                        }
                                    }
                                ]
                                break;
                            case 'salesman_change':
                                this.columns = [
                                    {
                                        title: '绑定店员信息',
                                        key: 'store_name',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '变更类型',
                                        key: 'group_name',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '变更原因',
                                        key: 'type_name',
                                        minWidth: 120,
                                    },
                                    {
                                        title: '变更时间',
                                        key: 'add_time',
                                        minWidth: 120,
                                    },
                                ]
                                break;
                            default:
                                this.columns = [
                                    {
                                        title: 'ID',
                                        key: 'uid',
                                        minWidth: 120
                                    },
                                    {
                                        title: '昵称',
                                        key: 'nickname',
                                        minWidth: 120
                                    },
                                    {
                                        title: '等级',
                                        key: 'type',
                                        minWidth: 120
                                    },
                                    {
                                        title: '加入时间',
                                        key: 'add_time',
                                        minWidth: 120
                                    }
                                ]
                        }
                        this.loading = false;
                    } else {
                        this.loading = false;
                        this.$Message.error(res.msg);
                    }
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                })
            },
            showReportDetailDialog (report) {
                console.log(report, '9999')
                this.reportDetail = report;
                this.detail(report)
                this.reportDetailModal = true;
            }
        },
        mounted () {
            const canvas = document.getElementById('gauge');
            if (canvas) {
                canvas.width = 230;
                canvas.height = 135;
            }
        }
    }
</script>

<style lang="less" scoped>
    /deep/.ivu-tabs-nav-prev,
    /deep/.ivu-tabs-nav-next {
        line-height: 39px;
    }
    /deep/.ivu-modal-body {
        padding: 0;
    }
    .user-row {
        padding: 30px 35px 0;

        &-text {
            flex: 1;
            align-self: center;
        }

        &-action {
            .ivu-btn {
                margin-left: 12px;
                font-size: 13px !important;
                color: rgba(0, 0, 0, 0.85);

                &:first-child {
                    margin-left: 0;
                }

                &.ivu-btn-primary {
                    border-color: #1890ff;
                    background-color: #1890ff;
                    color: #ffffff;
                }

                &.ivu-btn-success {
                    border-color: #00c050;
                    background-color: #00c050;
                    color: #ffffff;
                }
            }
        }

        .nickname {
            font-weight: 500;
            font-size: 16px;
            line-height: 16px;
            color: rgba(0, 0, 0, 0.85);
        }

        .iconfont {
            margin-left: 7px;
            font-size: 18px;

            &:nth-child(2) {
                margin-left: 9px;
            }

            &.iconxiaochengxu {
                color: #007dff;
            }

            &.icongongzhonghao {
                color: #00bf00;
            }

            &.iconPC {
                color: #f69b00;
            }

            &.iconh5 {
                color: #9f5ce3;
            }

            &.iconapp {
                color: #e36734;
            }
        }

        .level {
            margin-top: 5px;

            img {
                width: 42px;
                height: 20px;
                vertical-align: middle;

                + span {
                    margin-left: 7px;
                }
            }

            .vip {
                display: inline-block;
                width: 56px;
                height: 26px;
                padding-left: 30px;
                background: url('../../../../assets/images/vip-bg.png') left
                    top/100% 100% no-repeat;
                font-weight: bold;
                font-size: 9px;
                line-height: 26px;
                color: #5f7db5;
                transform-origin: left;
                transform: scale(0.75, 0.75);
                vertical-align: middle;
            }
        }
    }

    .info-row {
        flex-wrap: nowrap;
        padding: 20px 35px 24px;

        &-item {
            flex: none;
            width: 155px;
            font-size: 14px;
            line-height: 14px;
            color: rgba(0, 0, 0, 0.85);

            &-title {
                margin-bottom: 12px;
                font-size: 13px;
                line-height: 13px;
                color: #666666;
            }
        }
    }

    .ivu-tabs {
        color: rgba(0, 0, 0, 0.85);

        /deep/ .ivu-tabs-bar {
            border-bottom: 0;
            margin-bottom: 0;
            background-color: #f5f7fa;

            .ivu-tabs-nav-container {
                font-size: 13px;
            }

            .ivu-tabs-ink-bar {
                display: none;
            }

            .ivu-tabs-tab {
                padding: 7px 19px !important;
                margin-right: 0;
                line-height: 26px;
            }

            .ivu-tabs-tab-active {
                background-color: #ffffff;
                color: rgba(0, 0, 0, 0.85);

                &:before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 2px;
                    background-color: #1890ff;
                }
            }
        }

        /deep/ .ivu-tabs-content {
            .ivu-tabs-tabpane {
                padding: 15px 15px !important;

                &:first-child {
                    padding: 0 25px !important;
                }
            }
        }

        .product {
            display: flex;

            .image {
                width: 50px;
                height: 50px;
            }

            img {
                width: 100%;
                height: 100%;
                border-radius: 4px;
            }

            .title {
                flex: 1;
                padding-left: 13px;
                text-align: left;
            }
        }
    }

    .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .dashboard-workplace {
        &-header {
            &-avatar {
                width: 64px;
                height: 64px;
                border-radius: 50%;
                margin-right: 16px;
                font-weight: 600;
            }

            &-tip {
                width: 82%;
                display: inline-block;
                vertical-align: middle;

                &-title {
                    font-size: 13px;
                    color: #000000;
                    margin-bottom: 12px;
                }

                &-desc {
                    &-sp {
                        width: 33.33%;
                        color: #17233d;
                        font-size: 13px;
                        display: inline-block;
                    }
                }
            }

            &-extra {
                .ivu-col {
                    p {
                        text-align: right;
                    }

                    p:first-child {
                        span:first-child {
                            margin-right: 4px;
                        }

                        span:last-child {
                            color: #808695;
                        }
                    }

                    p:last-child {
                        font-size: 22px;
                    }
                }
            }
        }
    }

    .container {
        padding: 20px;
        background-color: #f5f5f5;
        height: 100%;
    }

    .gauge-canvas {
        width: 230px;
        height: 135px;
    }

    .report-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative; /* 添加 position 属性 */
    }

    .report-cards {
        height: 480px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: relative; /* 添加 position 属性 */
        z-index: 1; /* 设置 z-index 为 1，使其位于下层 */
    }

    .report-title {
        color: white;
        padding: 15px;
        text-align: left;
        font-size: 30px;
        font-weight: Semibold;
    }

    .report-subtitle {
        margin-top: 30px;
        color: white;
        padding: 10px;
        font-size: 14px;
        width: 150px;
        height: 40px;
        margin-left: 20px;
        text-align: left;
        border-radius: 10px;
        background-color: #1272cb; /* 基础背景色 */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5); /* 添加阴影效果 */
    }

    .score-container {
        position: absolute;
        top: 60px;
        right: -40px;
    }

    .score-gauge {
        width: 120px;
        height: 120px;
    }

    .score-value {
        font-size: 24px;
        font-weight: bold;
        margin-left: 10px;
    }

    .report-content {
        padding: 20px;
        background-color: #eff8ff;
        border-radius: 10px;
        position: absolute;
        top: 20px;
        width: calc(128% - 40px);
        z-index: 2; /* 设置 z-index 为 2，使其位于上层 */
        margin-top: 220px;
        margin-left: 20px;
    }

    .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .section-titleBBB {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-left: 20px;
    }

    .section-text {
        font-size: 16px;
        font-weight: bold;
        color: #4a90e2;
    }

    .report-details {
        line-height: 1.6;
        color: #666;
    }

    .body-analysis {
        padding: 20px;
        // border-top: 1px solid #eee;
    }

    .body-analysisS {
        padding: 10px 0px 0px 40px;
        background-size: cover;
        background-position: center;
        position: relative; /* 添加 position 属性 */
        background-color: #f0f8ff;
        // border-radius: 10px;
        height: 40px; /* 设置背景图的高度 */
    }

    .analysis-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .analysis-titles {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        z-index: 2; /* 确保文字显示在背景图的上层 */
    }

    .analysis-text {
        font-size: 16px;
        font-weight: bold;
        color: #4a90e2;
    }

    .analysis-texts {
        font-size: 16px;
        font-weight: bold;
        color: white;
    }

    .body-outline {
        position: relative;
        height: 400px;
        background-color: #f0f8ff;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .body-icon {
        width: 200px;
        height: 400px;
        border-radius: 100px;
        position: relative;
    }

    .body-labels {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .label {
        position: absolute;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        color: #333;
    }

    /* 疾病部分样式 */
    .disease-section {
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        margin-left: 20px;
        margin-right: 20px;
        // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* 疾病部分样式 */
    .disease-sectionAAA {
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        margin-right: 20px;
    }

    .disease-details {
        line-height: 1.6;
        color: #444;
        padding: 10px;
        background-color: white;
        border-radius: 8px;
        // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .disease-text {
        margin-bottom: 8px;
        font-size: 14px;
    }

    .Adisease-section {
        background-color: white;
        border-radius: 10px;
        margin-left: 20px;
        margin-right: 20px;
    }

    .image-container {
        width: 300px;
        height: 300px;
        border-radius: 10px;
        overflow: hidden;
        margin: 20px 0;
        position: relative;
    }

    .tongue-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }
    .background {
        width: 62%;
        height: 70%;
        background-position: center; /* 背景图片居中 */
        background-repeat: no-repeat; /* 不重复背景图片 */
        background-size: cover; /* 背景图片覆盖整个容器 */
        position: absolute;
        margin-top: -88%;
        margin-left: 19%;
        border-radius: 15px;
        z-index: 2;
    }
</style>
<style scoped lang="stylus">
    .user_menu >>> .ivu-menu
        width 100% !important
</style>
