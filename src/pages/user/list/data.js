export default [
    {
        project: 'iView',
        desc: '对比的基本思想是，要避免页面上的元素太过相似。如果元素（字体、颜色、大小、线宽、形状、空间等）不相同，那就干脆让它们截然不同。要让页面引人注意，对比通常是最重要的一个因素，正是它能使读者首先看这个页面。',
        icon: 'https://dev-file.iviewui.com/p50TGdvvpXWVR06Vu2TAwkpRnpt8FURA/avatar'
    },
    {
        project: 'iView Pro',
        desc: '对比的基本思想是，要避免页面上的元素太过相似。如果元素（字体、颜色、大小、线宽、形状、空间等）不相同，那就干脆让它们截然不同。要让页面引人注意，对比通常是最重要的一个因素，正是它能使读者首先看这个页面。',
        icon: 'https://dev-file.iviewui.com/WLXm7gp1EbLDtvVQgkeQeyq5OtDm00Jd/avatar'
    },
    {
        project: 'iView Admin Pro',
        desc: '对比的基本思想是，要避免页面上的元素太过相似。如果元素（字体、颜色、大小、线宽、形状、空间等）不相同，那就干脆让它们截然不同。要让页面引人注意，对比通常是最重要的一个因素，正是它能使读者首先看这个页面。',
        icon: 'https://dev-file.iviewui.com/fAenQ8nvRjL7x0i0jEfuDBZHvJfHf3v6/avatar'
    },
    {
        project: 'iView Developer',
        desc: '对比的基本思想是，要避免页面上的元素太过相似。如果元素（字体、颜色、大小、线宽、形状、空间等）不相同，那就干脆让它们截然不同。要让页面引人注意，对比通常是最重要的一个因素，正是它能使读者首先看这个页面。',
        icon: 'https://dev-file.iviewui.com/ttkIjNPlVDuv4lUTvRX8GIlM2QqSe0jg/avatar'
    },
    {
        project: 'iView Run',
        desc: '对比的基本思想是，要避免页面上的元素太过相似。如果元素（字体、颜色、大小、线宽、形状、空间等）不相同，那就干脆让它们截然不同。要让页面引人注意，对比通常是最重要的一个因素，正是它能使读者首先看这个页面。',
        icon: 'https://dev-file.iviewui.com/4Z0QR2L0J1XStxBh99jVJ8qLfsGsOgjU/avatar'
    }
];
