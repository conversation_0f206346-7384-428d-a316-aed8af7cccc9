<template>
  <!-- 商品-商品分类 -->
  <div class="article-manager">
    <Card :bordered="false" dis-hover class="ivu-mt" :padding="0">
      <div class="new_card_pd">
        <!-- 筛选条件 -->
        <Form ref="artFrom" :model="artFrom" :label-width="labelWidth" inline :label-position="labelPosition"
          @submit.native.prevent>
          <FormItem label="关键字" label-for="status2">
            <Input placeholder="量表名称" v-model="artFrom.keywords" clearable class="input-add mr14" />
          </FormItem>

          <Button type="primary" @click="userSearchs()">查询</Button>
          <Button class="ResetSearch" style="margin-left: 20px;" @click="reset('artFrom')">重置</Button>
        </Form>
      </div>
    </Card>
    <Card :bordered="false" dis-hover class="ivu-mt">
      <!-- 商品分类表格 -->
      <vxe-table :data="tableData" ref="xTable" class="ivu-mt" highlight-hover-row :loading="loading"
        header-row-class-name="false">
        <vxe-table-column field="id" title="ID" tooltip></vxe-table-column>
        <vxe-table-column field="title" title="名称" tooltip></vxe-table-column>
        <vxe-table-column field="qr_code" title="分享二维码" tooltip>
          <template v-slot="{ row }">
            <img
              v-if="row.qr_code"
              :src="row.qr_code"
              style="width: 56px; height: 56px; cursor: pointer; border-radius: 4px;"
              @click="previewQRCode(row.qr_code)"
            />
            <span v-else style="color: #999;">无图片</span>
          </template>
        </vxe-table-column>
        <vxe-table-column field="cover_img" title="封面图" tooltip>
          <template v-slot="{ row }">
            <img :src="row.cover_img" style="width: 56px; height: 56px" />
          </template>
        </vxe-table-column>
        <vxe-table-column field="questions_num" title="题数"></vxe-table-column>
        <vxe-table-column field="estimated_time" title="预计答题时长" tooltip></vxe-table-column>
        <vxe-table-column field="user_num" title="参与人数" tooltip="true"></vxe-table-column>
        <vxe-table-column field="finish_num" title="完成人数" tooltip="true"></vxe-table-column>
        <vxe-table-column field="real_num" title="生成报告数" tooltip="true"></vxe-table-column>
        <vxe-table-column field="is_repeated" title="重复测评" tooltip="true">
          <template v-slot="{ row }">
            <i-switch v-model="row.is_repeated" :value="row.is_repeated" :true-value="1" :false-value="0"
              @on-change="onchangeIsRepeated(row)" size="large">
              <span slot="open">是</span>
              <span slot="close">否</span>
            </i-switch>
          </template>
        </vxe-table-column>
        <vxe-table-column field="status" title="启用状态" tooltip="true">
          <template v-slot="{ row }">
            <i-switch v-model="row.status" :value="row.status" :true-value="1" :false-value="0"
              @on-change="onchangeStatus(row)" size="large">
              <span slot="open">显示</span>
              <span slot="close">隐藏</span>
            </i-switch>
          </template>
        </vxe-table-column>
        <vxe-table-column field="date" title="操作">
          <template v-slot="{ row, index }">
            <a @click="edit(row)">编辑</a>
            <a @click="showScaleDetail(row)" style="margin-left:20px">详情</a>
          </template>
        </vxe-table-column>
      </vxe-table>
      <div class="ivu-mt ivu-text-right">
        <Page :total="total" :current="artFrom.page" :page-size="artFrom.limit" @on-change="changePatientPages"
          show-elevator show-total />
      </div>
    </Card>
    <Modal v-model="reportDetailModal" title="详情" :width="450" :footer-hide="true" :mask-closable="false">
      <div v-if="reportDetail">
        <div class="container">
          <div class="report-card">
            <div class="report-cards"
              :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/gback.png)' }">
              <div class="report-title">
                <div class="report-text">{{ physique_name }}</div>
              </div>
              <div class="report-subtitle">
                <div class="subtitle-text">{{ syndrome_name }}</div>
              </div>
              <div class="score-container">
                <canvas class="gauge-canvas" id="gauge"></canvas>
              </div>
              <div style="float: right; margin-right: 20px">
                <img :src="'https://67686161.com' + '/statics/images/product/xlog.png'"
                  style="width: 100px; height: 100px" />
              </div>
            </div>
            <div class="report-content">
              <div class="section-title">
                <img class="set_icon" style="width: 10px; height: 10px; margin-right: 10px;"
                  :src="'https://67686161.com' + '/statics/images/product/titles.png'" />
                <div class="section-text">体质辨识</div>
              </div>
              <div class="report-details">
                <div class="detail-text">{{ syndrome_introduction }}</div>
              </div>
            </div>
            <div class="body-analysis">
              <div class="analysis-title">
                <div class="analysis-text">体质分析</div>
              </div>
              <div class="body-outline"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/people.png)' }">
                <div class="body-icon"></div>
                <div class="body-labels">
                  <!-- 左边标签 -->
                  <div class="label" v-for="(answer, index) in leftAnswers" :key="`left-${index}`"
                    :style="{ top: 3 + index * 15 + '%', left: '5%' }">
                    {{ answer.name }}
                  </div>
                  <!-- 右边标签 -->
                  <div class="label" v-for="(answer, index) in rightAnswers" :key="`right-${index}`"
                    :style="{ top: 3 + index * 15 + '%', left: '75%' }">
                    {{ answer.name }}
                  </div>
                </div>
              </div>
            </div>
            <!-- 冠心病 -->
            <div class="disease-section">
              <div class="disease-details">
                <div class="disease-text">{{ physique_analysis }}</div>
              </div>
            </div>

            <div class="report-card" style="margin-top: 20px;">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">典型特征</div>
                </div>
              </div>
              <!-- 典型特征 -->
              <div class="disease-section">
                <div class="disease-details">
                  <div class="disease-text">{{ typical_symptom }}</div>
                </div>
              </div>
            </div>

            <div class="report-card" style="margin-top: 20px;">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">风险预警</div>
                </div>
              </div>
              <!-- 风险预警 -->
              <div class="Adisease-section">
                <div class="disease-details">
                  <div class="disease-text">{{ risk_warning }}</div>
                </div>
              </div>
            </div>
            <!-- 舌面识别 -->
            <div class="report-card" style="margin-top: 20px;">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">舌面识别</div>
                </div>
              </div>
              <div class="disease-sectionAAA">
                <div class="image-container">
                  <img class="tongue-image" :src="'https://67686161.com' + '/statics/images/product/bj.png'"></img>
                  <div class="background" :style="{ backgroundImage: 'url(' + surl + ')' }"></div>
                </div>
                <div v-for="(item, index) in shefeatures" style="margin-top: 20px;margin-bottom: 20px;">
                  <div class="section-titleBBB">
                    <img class="set_icon" style="width: 10px;height: 10px;margin-right: 10px;"
                      :src="'https://67686161.com' + '/statics/images/product/titles.png'"></img>
                    <div class="section-text">{{ item.feature_group }}</div>
                  </div>
                  <div class="section-title">
                    <div class="section-text" style="margin-left: 40px;">{{ item.feature_name }}</div>
                  </div>
                  <div class="disease-details">
                    <div class="disease-text">{{ item.feature_interpret }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!--面部识别-->
            <div class="report-card" style="margin-top: 20px;">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">面部识别</div>
                </div>
              </div>
              <div class="disease-sectionAAA">
                <div class="image-container">
                  <img class="tongue-image" :src="'https://67686161.com' + '/statics/images/product/bj.png'"></img>
                  <div class="background" :style="{ backgroundImage: 'url(' + ff_image + ')' }"></div>
                </div>
                <div v-for="(item, index) in featuresface" style="margin-top: 20px;margin-bottom: 20px;">
                  <div class="section-titleBBB">
                    <img class="set_icon" style="width: 10px;height: 10px;margin-right: 10px;"
                      :src="'https://67686161.com' + '/statics/images/product/titles.png'"></img>
                    <div class="section-text">{{ item.feature_group }}</div>
                  </div>
                  <div class="section-title">
                    <div class="section-text" style="margin-left: 40px;">{{ item.feature_name }}</div>
                  </div>
                  <div class="disease-details">
                    <div class="disease-text">{{ item.feature_interpret }}</div>
                  </div>
                </div>

              </div>
            </div>

            <!-- 运动建议 -->
            <div class="report-card" style="margin-top: 20px;" v-for="(item, index) in sport" :key="`sport-${index}`">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">{{ item.title }}</div>
                </div>
              </div>
              <!-- 运动建议 -->
              <div class="Adisease-section">
                <div class="disease-details">
                  <div class="disease-text">{{ item.advice }}</div>
                </div>
              </div>
            </div>

            <!-- 饮食建议 -->
            <div class="report-card" style="margin-top: 20px;" v-for="(item, index) in food" :key="`food-${index}`">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">{{ item.title }}</div>
                </div>
              </div>
              <!-- 饮食建议 -->
              <div class="Adisease-section">
                <div class="disease-details">
                  <div class="disease-text">{{ item.advice }}</div>
                </div>
              </div>
            </div>

            <!-- 外治建议 -->
            <div class="report-card" style="margin-top: 20px;" v-for="(item, index) in treatment"
              :key="`treatment-${index}`">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">{{ item.title }}</div>
                </div>
              </div>
              <!-- 外治建议 -->
              <div class="Adisease-section">
                <div class="disease-details">
                  <div class="disease-text">{{ item.advice }}</div>
                </div>
              </div>
            </div>

            <!--音乐建议-->
            <div class="report-card" style="margin-top: 20px;" v-for="(item, index) in music" :key="`music-${index}`">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">{{ item.title }}</div>
                </div>
              </div>
              <!--音乐建议-->
              <div class="Adisease-section">
                <div class="disease-details">
                  <div class="disease-text">{{ item.advice }}</div>
                </div>
              </div>
            </div>

            <!--起居建议-->
            <div class="report-card" style="margin-top: 20px;" v-for="(item, index) in sleep" :key="`sleep-${index}`">
              <div class="body-analysisS"
                :style="{ backgroundImage: 'url(' + 'https://67686161.com' + '/statics/images/product/titleback.png)', margin: '20px' }">
                <div class="analysis-titles">
                  <div class="analysis-texts">{{ item.title }}</div>
                </div>
              </div>
              <!--起居建议-->
              <div class="Adisease-section">
                <div class="disease-details">
                  <div class="disease-text">{{ item.advice }}</div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </Modal>
    <!-- 编辑量表弹窗 -->
    <Modal v-model="showEditModal" title="编辑量表" width="60%" height="60%" :mask-closable="false" class-name="edit-scale-modal">
      <Tabs v-model="activeTab">
        <!-- 基础信息 -->
        <TabPane label="基础信息" name="basic">
          <Form :model="editForm" label-position="left" :label-width="110">
            <!-- 量表名称 -->
            <FormItem label="量表名称">
              <Input v-model="editForm.title" placeholder="请输入量表名称" />
            </FormItem>

            <!-- 作答时间 -->
            <FormItem label="作答时间(分钟)">
              <InputNumber v-model="editForm.estimatedTime" :min="1" :max="999" style="width: 100%" />
            </FormItem>

            <!-- 是否可重复填写 -->
            <FormItem label="是否可重复填写">
              <Select v-model="editForm.isRepeated" style="width: 100%">
                <Option value="1">是</Option>
                <Option value="0">否</Option>
              </Select>
            </FormItem>

            <!-- 已测人数 -->
            <FormItem label="已测人数">
              <InputNumber v-model="editForm.userNum" :min="0" style="width: 100%" />
            </FormItem>
          </Form>
        </TabPane>

        <TabPane label="图片配置" name="images">
          <Form :model="editForm" label-position="left" :label-width="110">
            <FormItem label="量表轮播图">
              <p style="font-size:12px;color:#999;margin-bottom:6px;">最多可上传 6 张，建议尺寸 750×516</p>
              <Upload multiple :before-upload="handleBannerUpload" :format="['jpg', 'jpeg', 'png']" :max-size="2048"
                action="">
                <Button icon="ios-cloud-upload-outline">上传图片</Button>
              </Upload>

              <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:5px">
                <div v-for="(img, idx) in editForm.banner" :key="idx" style="position:relative;">
                  <img :src="img.img" width="50" height="50" style="object-fit:cover;border-radius:4px" />
                  <Icon type="ios-trash-outline" size="20" @click="removeImage('banner', idx)"
                    style="font-size:15px;position:absolute;top:2px;right:2px;color:#fff;background:#ed4014;border-radius:50%;cursor:pointer" />
                </div>
              </div>
            </FormItem>


            <FormItem label="量表分享图">
              <p style="font-size:12px;color:#999;margin-bottom:6px;">建议建议图片尺寸为正方形，尺寸 400×400</p>
              <Upload ref="shareImgUpload" :before-upload="beforeShareImgUpload" :show-upload-list="false"
                :format="['jpg', 'jpeg', 'png']" :max-size="2048" :on-format-error="handleFormatError"
                :on-exceeded-size="handleMaxSize" action="">
                <Button icon="ios-cloud-upload-outline">上传量表分享图</Button>
              </Upload>


              <div v-if="editForm.shareImg" class="demo-upload-list" style="margin-right: 5px; position: relative; display: inline-block;">
                <img :src="editForm.shareImg" style="width:50px;height:50px;margin-top:5px;object-fit:cover" />
                <Icon type="ios-trash-outline" size="16" @click="editForm.shareImg = ''"
                style="font-size:15px;position:absolute;top:0;right:0;color:#fff;background:#ed4014;border-radius:50%;cursor:pointer;"></Icon>
              </div>
            </FormItem>


            <FormItem label="客服二维码">
              <p style="font-size:12px;color:#999;margin-bottom:6px;">到企业微信管理后台下载客服二维码之后，上传图片</p>
              <Upload ref="keQrCodeUpload" :before-upload="beforeKeQrCodeUpload" :show-upload-list="false"
                :format="['jpg', 'jpeg', 'png']" :max-size="2048" :on-format-error="handleFormatError"
                :on-exceeded-size="handleMaxSize" action="">
                <Button icon="ios-cloud-upload-outline">上传客服二维码</Button>
              </Upload>


              <div v-if="editForm.keQrCode" class="demo-upload-list" style="margin-right: 5px; position: relative; display: inline-block;">
                <img :src="editForm.keQrCode" style="width:50px;height:50px;margin-top:5px;object-fit:cover" />
                <Icon type="ios-trash-outline" size="16" @click="editForm.keQrCode = ''"
                style="font-size:15px;position:absolute;top:0;right:0;color:#fff;background:#ed4014;border-radius:50%;cursor:pointer;"></Icon>
              </div>
            </FormItem>


            <FormItem label="量表封面图">
              <p style="font-size:12px;color:#999;margin-bottom:6px;">建议为正方形图片，建议尺寸 160×160</p>
              <Upload ref="coverImgUpload" :before-upload="beforeCoverImgUpload" :show-upload-list="false"
                :format="['jpg', 'jpeg', 'png']" :max-size="2048" :on-format-error="handleFormatError"
                :on-exceeded-size="handleMaxSize" action="">
                <Button icon="ios-cloud-upload-outline">上传量表封面图</Button>
              </Upload>


              <div v-if="editForm.coverImg" class="demo-upload-list" style="margin-right: 5px; position: relative; display: inline-block;">
                <img :src="editForm.coverImg" style="width:50px;height:50px;margin-top:5px;object-fit:cover" />
                <Icon type="ios-trash-outline" size="16" @click="editForm.coverImg = ''"
                style="font-size:15px;position:absolute;top:0;right:0;color:#fff;background:#ed4014;border-radius:50%;cursor:pointer;"></Icon>
              </div>
            </FormItem>
          </Form>
        </TabPane>

        <!-- 量表详情 -->
        <TabPane label="量表详情" name="content">
          <wangeditor style="width: 100%" :content="editForm.content"></wangeditor>
        </TabPane>

        <!-- 协议详情 -->
        <TabPane label="协议详情" name="agreement">
          <wangeditor style="width: 100%" :content="editForm.agreement"></wangeditor>
        </TabPane>

        <!-- 报告说明 -->
        <TabPane label="报告说明" name="reportIntro">
          <wangeditor style="width: 100%" :content="editForm.reportIntro"></wangeditor>
        </TabPane>
      </Tabs>

      <!-- 底部按钮 -->
      <div slot="footer" style="text-align:right">
        <Button @click="showEditModal = false">取消</Button>
        <Button type="primary" @click="handleSaveEdit">保存</Button>
      </div>
    </Modal>

    <!-- 量表详情查看弹窗 -->
    <Modal v-model="showDetailModal" title="量表详情" width="60%" :mask-closable="false" class-name="detail-scale-modal">
      <Tabs v-model="detailActiveTab">
        <!-- 基础信息 -->
        <TabPane label="基础信息" name="basic">
          <Form :model="detailForm" label-position="left" :label-width="110">
            <!-- 量表名称 -->
            <FormItem label="量表名称">
              <div class="detail-text">{{ detailForm.title || '暂无' }}</div>
            </FormItem>

            <!-- 作答时间 -->
            <FormItem label="作答时间(分钟)">
              <div class="detail-text">{{ detailForm.estimatedTime || '暂无' }}</div>
            </FormItem>

            <!-- 是否可重复填写 -->
            <FormItem label="是否可重复填写">
              <div class="detail-text">{{ detailForm.isRepeated === '1' ? '是' : '否' }}</div>
            </FormItem>

            <!-- 已测人数 -->
            <FormItem label="已测人数">
              <div class="detail-text">{{ detailForm.userNum || '0' }}</div>
            </FormItem>
          </Form>
        </TabPane>

        <TabPane label="图片配置" name="images">
          <Form :model="detailForm" label-position="left" :label-width="110">
            <FormItem label="量表轮播图">
              <div v-if="detailForm.banner && detailForm.banner.length > 0" style="display:flex;flex-wrap:wrap;gap:8px;">
                <div v-for="(img, idx) in detailForm.banner" :key="idx" style="position:relative;">
                  <img :src="img.img" width="80" height="80" style="object-fit:cover;border-radius:4px" />
                </div>
              </div>
              <div v-else class="detail-text">暂无图片</div>
            </FormItem>

            <FormItem label="量表分享图">
              <div v-if="detailForm.shareImg">
                <img :src="detailForm.shareImg" style="width:80px;height:80px;object-fit:cover;border-radius:4px" />
              </div>
              <div v-else class="detail-text">暂无图片</div>
            </FormItem>

            <FormItem label="客服二维码">
              <div v-if="detailForm.keQrCode">
                <img :src="detailForm.keQrCode" style="width:80px;height:80px;object-fit:cover;border-radius:4px" />
              </div>
              <div v-else class="detail-text">暂无图片</div>
            </FormItem>

            <FormItem label="量表封面图">
              <div v-if="detailForm.coverImg">
                <img :src="detailForm.coverImg" style="width:80px;height:80px;object-fit:cover;border-radius:4px" />
              </div>
              <div v-else class="detail-text">暂无图片</div>
            </FormItem>
          </Form>
        </TabPane>

        <!-- 量表详情 -->
        <TabPane label="量表详情" name="content">
          <div class="rich-text-content" v-html="detailForm.content || '<p>暂无内容</p>'"></div>
        </TabPane>

        <!-- 协议详情 -->
        <TabPane label="协议详情" name="agreement">
          <div class="rich-text-content" v-html="detailForm.agreement || '<p>暂无内容</p>'"></div>
        </TabPane>

        <!-- 报告说明 -->
        <TabPane label="报告说明" name="reportIntro">
          <div class="rich-text-content" v-html="detailForm.reportIntro || '<p>暂无内容</p>'"></div>
        </TabPane>
      </Tabs>

      <!-- 底部按钮 -->
      <div slot="footer" style="text-align:right">
        <Button @click="showDetailModal = false">关闭</Button>
      </div>
    </Modal>

    <!-- 二维码图片预览弹窗 -->
    <Modal v-model="qrCodePreviewModal" title="二维码预览" width="30%" :mask-closable="false">
      <div class="qr-preview-container">
        <div class="qr-preview-content">
          <img :src="previewQRCodeUrl" class="preview-qr-image" />
        </div>
        <div class="qr-preview-actions">
          <Button type="primary" icon="ios-download-outline" @click="downloadQRCode">下载二维码</Button>
          <Button @click="qrCodePreviewModal = false">关闭</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapState } from "vuex";
import axios from "axios";
import util from "@/libs/util";
import Setting from "@/setting";
import wangeditor from "@/components/wangEditor/index.vue";
import {
  scalesList,
  scalesDetail,
  scalesSave,
  updatesFieldValue,
} from "@/api/user";

export default {
  name: "product_productClassify",
  data() {
    return {
      showEditModal: false, // 弹窗显隐
      activeTab: "basic", // 默认标签
      showDetailModal: false, // 详情查看弹窗显隐
      detailActiveTab: "basic", // 详情查看默认标签
      qrCodePreviewModal: false, // 二维码预览弹窗显隐
      previewQRCodeUrl: "", // 预览二维码URL
      editForm: {
        title: "",
        estimatedTime: "",
        isRepeated: "0", // 初始值设为字符串类型，与Option的value类型一致
        banner: [],
        keQrCode: "", // 单张图片URL
        shareImage: "", // 单张
        qrCode: "", // 单张
        shareImg: "", // 单张
        content: "",
        agreement: "",
        reportIntro: "",
        userNum: "",
        coverImg: "",
        id: 0,
      },
      detailForm: {
        title: "",
        estimatedTime: "",
        isRepeated: "0",
        banner: [],
        keQrCode: "",
        shareImg: "",
        content: "",
        agreement: "",
        reportIntro: "",
        userNum: "",
        coverImg: "",
        id: 0,
      },
      grid: {
        xl: 7,
        lg: 7,
        md: 12,
        sm: 24,
        xs: 24,
      },
      loading: false,
      artFrom: {
        page: 1,
        keywords: "",
        limit: 10,
      },
      total: 0,
      tableData: [],
      showModal: false,
      reportDetailModal: false, // 舌诊报告详情弹窗状态
      reportDetail: null, // 舌诊报告详情数据
      score: "", // 示例分数
      resData: [],
      physique_name: "",
      physique_analysis: "",
      syndrome_introduction: "",
      syndrome_name: "",
      answers: [],
      food: [],
      music: [],
      sport: [],
      sleep: [],
      treatment: [],
      surl: "",
      ff_image: "",
      risk_warning: "",
      featuresface: [],
      shefeatures: [],
      typical_symptom: "",
      typical_symptom_arr: [],
      colors: ["#4a90e2", "#ff7e5f", "#50e3c2", "#f6b93b", "#9e9e9e"],
      fontSizes: [12, 14, 16, 18, 20],
      detailId: "",
    };
  },
  components: {
    wangeditor,
  },
  computed: {
    ...mapState("admin/layout", ["isMobile"]),
    ...mapState("admin/userLevel", ["categoryId"]),
    labelWidth() {
      return this.isMobile ? undefined : 96;
    },
    labelPosition() {
      return this.isMobile ? "top" : "right";
    },
    leftAnswers() {
      return this.answers.filter((answer, index) => index % 2 === 0);
    },
    rightAnswers() {
      return this.answers.filter((answer, index) => index % 2 === 1);
    },
  },
  watch: {
    reportDetailModal(value) {
      if (!value) {
        // 弹窗关闭时清除 canvas
        const canvas = document.getElementById("gauge");
        if (canvas) {
          const ctx = canvas.getContext("2d");
          ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
      }
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 获取文件上传URL的辅助方法
    getUploadURL() {
      return Setting.fileUploadAPIURL;
    },
    edit(row) {
      var id = row.id;
      scalesDetail({ id: id }).then((res) => {
        this.editForm = {
          title: res.data.title,
          estimatedTime: res.data.estimated_time,
          isRepeated: String(res.data.is_repeated), // 确保类型为字符串，与Option的value类型一致
          banner: res.data.banner,
          keQrCode: res.data.ke_qr_code, // [{img:'xxx'},...]
          shareImg: res.data.share_img, // 单张
          content: res.data.content || "<p>量表详情内容</p>",
          agreement: res.data.agreement || "<p>协议详情内容</p>",
          reportIntro: res.data.report_intro || "<p>报告说明内容</p>",
          userNum: res.data.user_num,
          coverImg: res.data.cover_img,
          id: id,
        };
        this.activeTab = "basic";
        this.showEditModal = true;
      });
    },

    // 上传 banner 图
    async handleBannerUpload(file) {
      const formData = new FormData();
      formData.append("pid", 3);
      formData.append("file", file);

      try {
        const res = await axios.post(this.getUploadURL(), formData, {
          headers: {
            "Content-Type": "multipart/form-data",
            "Authori-zation": "Bearer " + util.cookies.get("token"),
          },
        });
        // 追加到轮播图数组，保持数据结构一致
        this.editForm.banner.push({ img: res.data.data.src });
      } catch (e) {
        this.$Message.error("轮播图上传失败");
      }
      return false;
    },

    // 上传 量表分享图
    async beforeShareImgUpload(file) {
      const formData = new FormData();
      formData.append("pid", 3);
      formData.append("file", file);

      try {
        const res = await axios.post(this.getUploadURL(), formData, {
          headers: {
            "Content-Type": "multipart/form-data",
            "Authori-zation": "Bearer " + util.cookies.get("token"),
          },
        });
        // 根据你后端返回格式取出 url
        this.editForm.shareImg = res.data.data.src; // 修复字段名
      } catch (e) {
        this.$Message.error("分享图上传失败");
      }
      return false; // 阻止 Upload 默认上传
    },

    // 上传 客服二维码
    async beforeKeQrCodeUpload(file) {
      const formData = new FormData();
      formData.append("pid", 3);
      formData.append("file", file);
      try {
        const res = await axios.post(this.getUploadURL(), formData, {
          headers: {
            "Content-Type": "multipart/form-data",
            "Authori-zation": "Bearer " + util.cookies.get("token"),
          },
        });
        // 根据你后端返回格式取出 url
        this.editForm.keQrCode = res.data.data.src;
      } catch (e) {
        this.$Message.error("客服二维码上传失败");
      }
      return false; // 阻止 Upload 默认上传
    },

    // 上传 量表封面图
    async beforeCoverImgUpload(file) {
      const formData = new FormData();
      formData.append("pid", 3);
      formData.append("file", file);
      try {
        const res = await axios.post(this.getUploadURL(), formData, {
          headers: {
            "Content-Type": "multipart/form-data",
            "Authori-zation": "Bearer " + util.cookies.get("token"),
          },
        });
        // 根据您后端返回格式取出 url
        this.editForm.coverImg = res.data.data.src;
      } catch (e) {
        this.$Message.error("封面图上传失败");
      }
      return false; // 阻止 Upload 默认上传
    },

    // 删除图片方法
    removeImage(type, index) {
      if (type === "banner") {
        this.editForm.banner.splice(index, 1);
      }
    },

    // 处理格式错误
    handleFormatError() {
      this.$Message.error("图片格式仅支持 jpg、png");
    },

    // 处理文件大小超限
    handleMaxSize() {
      this.$Message.error("图片大小不能超过 2M");
    },

    // 预览二维码
    previewQRCode(qrCodeUrl) {
      this.previewQRCodeUrl = qrCodeUrl;
      this.qrCodePreviewModal = true;
    },

    // 下载二维码
    downloadQRCode() {
      if (!this.previewQRCodeUrl) return;

      try {
        // 创建一个临时的a标签来下载图片
        const link = document.createElement("a");
        link.href = this.previewQRCodeUrl;

        // 从URL中提取文件名，如果没有则使用默认名称
        const fileName = this.previewQRCodeUrl.split("/").pop() || "qrcode.jpg";
        link.download = fileName;

        // 添加到DOM中并触发点击
        document.body.appendChild(link);
        link.click();

        // 清理DOM
        document.body.removeChild(link);

        this.$Message.success("下载已开始");
      } catch (error) {
        console.error("下载失败:", error);
        this.$Message.error('下载失败，请右键图片选择"图片另存为"');

        // 如果直接下载失败，尝试在新窗口打开图片
        window.open(this.previewQRCodeUrl, "_blank");
      }
    },

    onchangeIsRepeated(row) {
      let data = {
        id: row.id,
        field: "is_repeated",
        value: row.is_repeated,
      };
      updatesFieldValue(data)
        .then((res) => {
          this.$Message.success(res.msg);
          this.getList();
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },

    onchangeStatus(row) {
      let data = {
        id: row.id,
        field: "status",
        value: row.status,
      };
      updatesFieldValue(data)
        .then((res) => {
          this.$Message.success(res.msg);
          this.getList();
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },

    /**
     * 保存编辑
     */
    handleSaveEdit() {
      // TODO: 调用接口保存 this.editForm
      const formData = new FormData();
      formData.append("id", this.editForm.id);
      formData.append("title", this.editForm.title);
      formData.append("estimated_time", this.editForm.estimatedTime);
      formData.append("is_repeated", this.editForm.isRepeated);
      formData.append("banner", this.editForm.banner);
      formData.append("ke_qr_code", this.editForm.keQrCode);
      formData.append("share_img", this.editForm.shareImg);
      formData.append("content", this.editForm.content);
      formData.append("agreement", this.editForm.agreement);
      formData.append("report_intro", this.editForm.reportIntro);
      formData.append("user_num", this.editForm.userNum);
      formData.append("cover_img", this.editForm.coverImg);
      if (this.editForm.banner.length > 0) {
        var bannerImgs = [];
        this.editForm.banner.forEach((item) => {
          bannerImgs.push(item.img);
        });
        formData.append("banner", bannerImgs);
      }
      scalesSave(formData)
        .then((res) => {
          this.$Message.success(res.msg);
          this.showEditModal = false;
          this.getList();
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },
    // 显示量表详情
    showScaleDetail(row) {
      var id = row.id;
      scalesDetail({ id: id }).then((res) => {
        this.detailForm = {
          title: res.data.title,
          estimatedTime: res.data.estimated_time,
          isRepeated: String(res.data.is_repeated),
          banner: res.data.banner,
          keQrCode: res.data.ke_qr_code,
          shareImg: res.data.share_img,
          content: res.data.content || "<p>暂无内容</p>",
          agreement: res.data.agreement || "<p>暂无内容</p>",
          reportIntro: res.data.report_intro || "<p>暂无内容</p>",
          userNum: res.data.user_num,
          coverImg: res.data.cover_img,
          id: id,
        };
        this.detailActiveTab = "basic";
        this.showDetailModal = true;
      }).catch((res) => {
        this.$Message.error(res.msg || '获取详情失败');
      });
    },


    drawGauge() {
      // 获取 canvas 元素
      const canvas = document.getElementById("gauge");
      if (!canvas) {
        console.error("Canvas element not found");
        return;
      }

      // 获取 canvas 上下文
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        console.error("Canvas context not found");
        return;
      }
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      const radius = Math.min(centerX, centerY) - 10;
      const startAngle = Math.PI + Math.PI * 0.005; // 调整起始角度，闭合90%
      const endAngle = Math.PI * 2 - Math.PI * 0.005; // 调整结束角度
      const maxScore = 100;
      const minScore = 50;
      const percentage = (this.score - minScore) / (maxScore - minScore);
      const sweepAngle = (endAngle - startAngle) * percentage;

      // 绘制背景圆弧
      const ticks = [50, 60, 70, 80, 90, 100];
      const colors = ["#FE8B86", "#FEB936", "#9EE202", "#24ED05", "#21E303"]; // 颜色数组
      for (let i = 0; i < ticks.length - 1; i++) {
        const tickPercentage = (ticks[i] - minScore) / (maxScore - minScore);
        const nextTickPercentage =
          (ticks[i + 1] - minScore) / (maxScore - minScore);
        const tickAngle = startAngle + tickPercentage * (endAngle - startAngle);
        const nextTickAngle =
          startAngle + nextTickPercentage * (endAngle - startAngle);

        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, tickAngle, nextTickAngle);
        ctx.strokeStyle = colors[i];
        ctx.lineWidth = 10;
        ctx.stroke();
      }

      // 绘制刻度值（放在圆圈里面）
      for (let i = 0; i < ticks.length; i++) {
        const tickPercentage = (ticks[i] - minScore) / (maxScore - minScore);
        const tickAngle = startAngle + tickPercentage * (endAngle - startAngle);

        // 绘制刻度值
        ctx.font = "12px Arial";
        ctx.fillStyle = "#fff"; // 设置数字颜色为白色
        ctx.textAlign = "center";
        ctx.fillText(
          ticks[i].toString(),
          centerX + radius * 0.7 * Math.cos(tickAngle), // 调整位置，放在圆圈里面
          centerY + radius * 0.7 * Math.sin(tickAngle) + 5
        );
      }

      // 绘制指针（缩短指针长度）
      ctx.beginPath();
      ctx.moveTo(centerX, centerY); // 指针起始点
      ctx.lineTo(
        centerX + radius * 0.6 * Math.cos(startAngle + sweepAngle), // 指针终点（缩短长度）
        centerY + radius * 0.6 * Math.sin(startAngle + sweepAngle)
      );
      ctx.strokeStyle = "#fff";
      ctx.lineWidth = 3;
      ctx.stroke();

      // 绘制中心点
      ctx.beginPath();
      ctx.arc(centerX, centerY, 5, 0, Math.PI * 2);
      ctx.fillStyle = "#fff";
      ctx.fill();

      // 绘制分数
      ctx.font = "24px Arial";
      ctx.fillStyle = "#fff";
      ctx.textAlign = "center";
      ctx.fillText(this.score.toString(), centerX, centerY + 30); // 调整分数位置
    },
    updateScore(newScore) {
      this.score = newScore;
      this.drawGauge();
    },

    changePatientPages(page) {
      this.artFrom.page = page;
      this.getList();
    },
    // 列表
    getList() {
      this.loading = true;
      scalesList(this.artFrom)
        .then(async (res) => {
          this.tableData = res.data.list;
          this.total = res.data.count;
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$Message.error(res.msg);
        });
    },
    pageChange(index) {
      this.artFrom.page = index;
      this.getList();
    },
    reset() {
      this.artFrom.project = "";
      this.artFrom.keywords = ""; // 新增
      this.getList(this.artFrom);
    },
    // 表格搜索
    userSearchs() {
      this.artFrom.page = 1;
      this.getList();
    },
  },
};
</script>
<style scoped lang="stylus">
.treeSel >>>.ivu-select-dropdown-list {
  padding: 0 10px !important;
  box-sizing: border-box;
}

.tabBox_img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

/deep/.ivu-input {
  font-size: 14px !important;
}

.project-time-row {
  display: flex;
  margin-bottom: 10px;
}

.add-project-button {
  margin-left: 90px;
  margin-bottom: 30px;
  border: solid 1px #2D8CF0;
  color: #2D8CF0;
  line-hight: 10px;

  .suffix {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #1A1A1A;
    pointer-events: none;
  }
}
</style>
<style lang="less" scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  height: 100%;
}

.gauge-canvas {
  width: 230px;
  height: 135px;
}

.report-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  /* 添加 position 属性 */
}

.report-cards {
  height: 480px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  /* 添加 position 属性 */
  z-index: 1;
  /* 设置 z-index 为 1，使其位于下层 */
}

.report-title {
  color: white;
  padding: 15px;
  text-align: left;
  font-size: 30px;
  font-weight: Semibold;
}

.report-subtitle {
  margin-top: 30px;
  color: white;
  padding: 10px;
  font-size: 14px;
  width: 150px;
  height: 40px;
  margin-left: 20px;
  text-align: left;
  border-radius: 10px;
  background-color: #1272cb;
  /* 基础背景色 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  /* 添加阴影效果 */
}

.score-container {
  position: absolute;
  top: 60px;
  right: -40px;
}

.score-gauge {
  width: 120px;
  height: 120px;
}

.score-value {
  font-size: 24px;
  font-weight: bold;
  margin-left: 10px;
}

.report-content {
  padding: 20px;
  background-color: #eff8ff;
  border-radius: 10px;
  position: absolute;
  top: 20px;
  width: calc(128% - 40px);
  z-index: 2;
  /* 设置 z-index 为 2，使其位于上层 */
  margin-top: 220px;
  margin-left: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.section-titleBBB {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-left: 20px;
}

.section-text {
  font-size: 16px;
  font-weight: bold;
  color: #4a90e2;
}

.report-details {
  line-height: 1.6;
  color: #666;
}

.body-analysis {
  padding: 20px;
  // border-top: 1px solid #eee;
}

.body-analysisS {
  padding: 10px 0px 0px 40px;
  background-size: cover;
  background-position: center;
  position: relative;
  /* 添加 position 属性 */
  background-color: #f0f8ff;
  // border-radius: 10px;
  height: 40px;
  /* 设置背景图的高度 */
}

.analysis-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.analysis-titles {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  /* 确保文字显示在背景图的上层 */
}

.analysis-text {
  font-size: 16px;
  font-weight: bold;
  color: #4a90e2;
}

.analysis-texts {
  font-size: 16px;
  font-weight: bold;
  color: white;
}

.body-outline {
  position: relative;
  height: 400px;
  background-color: #f0f8ff;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.body-icon {
  width: 200px;
  height: 400px;
  border-radius: 100px;
  position: relative;
}

.body-labels {
  position: absolute;
  width: 100%;
  height: 100%;
}

.label {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 12px;
  color: #333;
}

/* 疾病部分样式 */
.disease-section {
  padding: 10px;
  background-color: white;
  border-radius: 10px;
  margin-left: 20px;
  margin-right: 20px;
  // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 疾病部分样式 */
.disease-sectionAAA {
  padding: 10px;
  background-color: white;
  border-radius: 10px;
  margin-right: 20px;
}

.disease-details {
  line-height: 1.6;
  color: #444;
  padding: 10px;
  background-color: white;
  border-radius: 8px;
  // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.disease-text {
  margin-bottom: 8px;
  font-size: 14px;
}

.Adisease-section {
  background-color: white;
  border-radius: 10px;
  margin-left: 20px;
  margin-right: 20px;
}

.image-container {
  width: 300px;
  height: 300px;
  border-radius: 10px;
  overflow: hidden;
  margin: 20px 0;
  position: relative;
}

.tongue-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.background {
  width: 62%;
  height: 70%;
  background-position: center;
  /* 背景图片居中 */
  background-repeat: no-repeat;
  /* 不重复背景图片 */
  background-size: cover;
  /* 背景图片覆盖整个容器 */
  position: absolute;
  margin-top: -88%;
  margin-left: 19%;
  border-radius: 15px;
  z-index: 2;
}

/* 二维码预览弹窗样式 */
.qr-preview-container {
  padding: 20px;
}

.qr-preview-content {
  text-align: center;
  margin-bottom: 20px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-qr-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.qr-preview-actions {
  text-align: center;
  border-top: 1px solid #e8eaec;
  padding-top: 15px;
}

.qr-preview-actions .ivu-btn {
  margin: 0 10px;
}

/* 表格中二维码图片悬停效果 */
.vxe-table img[style*="cursor: pointer"]:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 编辑量表弹窗：内容内部滚动，底部按钮固定可见 */
.edit-scale-modal .ivu-modal-body {
  max-height: calc(100vh - 220px);
  overflow: auto;
  padding-right: 12px;
}

.edit-scale-modal .ivu-modal-footer {
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 1;
}

/* 详情查看样式 */
.detail-text {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  color: #495057;
  min-height: 32px;
  line-height: 1.5;
}

.rich-text-content {
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  min-height: 200px;
  max-height: 100%;
  line-height: 1.6;
  overflow-y: auto;
}

.rich-text-content p {
  margin-bottom: 12px;
}

.rich-text-content img {
  max-width: 100%;
  height: auto;
}

/* 详情查看模态框样式 */
.detail-scale-modal .ivu-modal {
  top: 50px !important;
}

.detail-scale-modal .ivu-modal-wrap {
  overflow: hidden;
}

.detail-scale-modal .ivu-modal-content {
  height: calc(100vh - 100px);
  max-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.detail-scale-modal .ivu-modal-header {
  flex-shrink: 0;
  border-bottom: 1px solid #e8eaec;
}

.detail-scale-modal .ivu-modal-body {
  flex: 1;
  overflow: hidden;
  padding: 16px 24px;
}

.detail-scale-modal .ivu-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-scale-modal .ivu-tabs-bar {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.detail-scale-modal .ivu-tabs-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.detail-scale-modal .ivu-tabs-tabpane {
  height: 100%;
}

.detail-scale-modal .ivu-modal-footer {
  flex-shrink: 0;
  border-top: 1px solid #e8eaec;
  background: #fff;
}
</style>
