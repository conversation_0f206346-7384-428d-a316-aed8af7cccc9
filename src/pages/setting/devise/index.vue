<template>
    <!-- 添加主题-首页装修 -->
    <div class="diy-page">
        <PageHeader class="product_tabs"
                    :title="$route.meta.title"
                    hidden-breadcrumb>
            <div slot="title"
                 class="f_return acea-row row-between-wrapper">
                <div class="f_title acea-row row-middle">
                    <div class="acea-row row-middle"
                         @click="returnTap">
                        <div class="iconfont iconfanhui"></div>
                        <div class="return">返回</div>
                    </div>
                    <div class="mr20">
                        <span class="name">当前页面：{{nameTxt || '模板'}}</span>
                        <Poptip v-model="visible"
                                width="347">
                            <span class="iconfont iconzidingyicaidan"></span>
                            <template #content>
                                <div>
                                    <Input v-model="nameTxt"
                                           placeholder="必填不超过15个字"
                                           style="width: 200px"
                                           @on-change="changName"></Input>
                                    <Button type="text"
                                            @click="cancel">取消</Button>
                                    <Button type="primary"
                                            @click="determine">确定</Button>
                                </div>
                            </template>
                        </Poptip>
                    </div>
                </div>
                <div class="buttons">
                    <Button ghost
                            class="bnt w-74"
                            @click="preview"
                            v-if="pageId"><span class="iconfont iconshouyintai-yanjing"></span>预览</Button>
                    <Button ghost
                            class="bnt ml20 w-74"
                            @click="saveConfig(1)"
                            :loading="loading">仅保存</Button>
                    <Button class="release ml20 w-74"
                            @click="saveConfig(2)"
                            :loading="relLoading">保存关闭</Button>
                </div>
            </div>
        </PageHeader>
        <Card :bordered="false"
              dis-hover
              class="ivu-mt"
              style="margin-left: 10px;">
            <div class="diy-wrapper"
                 :style="'height:'+ clientHeight + 'px;'">
                <!-- 左侧 -->
                <div class="left">
                    <div class="wrapper"
                         :style="'height:'+ clientHeight + 'px;'">
                        <div class="list"
                             v-for="(item, index) in leftMenu"
                             :key="index">
                            <div class="tips"
                                 @click="item.isOpen = !item.isOpen">
                                {{ item.title }}
                                <div class="iconfont iconyou"
                                     v-if="!item.isOpen"></div>
                                <div class="iconfont iconxia"
                                     v-else></div>
                            </div>
                            <!-- 拖拽组件 -->
                            <draggable class="dragArea list-group"
                                       :list="item.list"
                                       :group="{ name: 'people', pull: 'clone', put: false }"
                                       :clone="cloneDog"
                                       dragClass="dragClass"
                                       filter=".search , .navbar , .homeComb , .service">
                                <div class="list-group-item"
                                     :class="{ search: element.cname == '搜索框' , navbar: element.cname == '选项卡' , homeComb: element.cname == '轮播搜索' , service: element.cname == '悬浮按钮'}"
                                     v-for="(element, index) in item.list"
                                     :key="element.id"
                                     @click="addDom(element, 1)"
                                     v-show="item.isOpen">
                                    <div>
                                        <div class="position"
                                             style="display: none">释放鼠标将组建添加到此处</div>
                                        <svg class="conter iconfont-diy icon svg-icon"
                                             aria-hidden="true">
                                            <use :xlink:href="element.icon"></use>
                                        </svg>
                                        <p class="conter">{{ element.cname }}</p>
                                    </div>
                                </div>
                            </draggable>
                        </div>
                    </div>
                </div>
                <!-- 中间自定义配置移动端页面 -->
                <div class="wrapper-con">
                    <div class="content">
                        <div class="contxt">
                            <div class="overflowy">
                                <div class="picture"><img src="@/assets/images/electric.png"></div>
                                <div class="page-title"
                                     :class="{ on: activeIndex == -100 }"
                                     @click="showTitle">
                                    {{ titleTxt }}
                                    <div class="delete-box"></div>
                                    <div class="handle"></div>
                                </div>
                            </div>
                            <div class="scrollCon"
                                 :style="'height:'+ rollHeight + 'px;'">
                                <div style="width: 460px;margin: 0 auto">
                                    <div class="scroll-box"
                                         :class="picTxt&&tabValTxt==2?'fullsize noRepeat':picTxt&&tabValTxt==1?'repeat ysize':'noRepeat ysize'"
                                         :style="'background-color:'+(colorTxt?colorPickerTxt:'')+';background-image: url('+(picTxt?picUrlTxt:'')+');min-height:'+ rollHeight + 'px;'"
                                         ref="imgContainer">
                                        <draggable class="dragArea list-group"
                                                   :list="mConfig"
                                                   group="people"
                                                   @change="log"
                                                   filter=".top"
                                                   :move="onMove"
                                                   animation="300">
                                            <div class="mConfig-item"
                                                 :class="{
														on: activeIndex == key,
														top: item.name == 'search_box' || item.name == 'nav_bar',
														hide: defaultArrays[item.num].isHide
													}"
                                                 v-for="(item, key) in mConfig"
                                                 :key="key"
                                                 @click.stop="bindconfig(item, key)"
                                                 :style="colorTxt?'background-color:'+colorPickerTxt+';':'background-color:#fff;'">
                                                <component :is="item.name"
                                                           ref="getComponentData"
                                                           :configData="propsObj"
                                                           :index="key"
                                                           :num="item.num"
                                                           :colorStyle='colorStyle'></component>
                                                <div class="delete-box">
                                                    <div class="handleType">
                                                        <div class="iconfont"
                                                             :class="defaultArrays[item.num].isHide?'iconyincang':'iconxianshi'"
                                                             @click.stop="bindHide(item)"></div>
                                                        <div class="iconfont iconshanchu3"
                                                             @click.stop="bindDelete(item, key)"></div>
                                                        <div class="iconfont icona-fuzhi1"
                                                             @click.stop="bindAddDom(item, 0, key)"></div>
                                                        <div class="iconfont iconshang"
                                                             :class="key===0?'on':''"
                                                             @click.stop="movePage(item, key, 1)"></div>
                                                        <div class="iconfont iconxia"
                                                             :class="key===mConfig.length-1?'on':''"
                                                             @click.stop="movePage(item, key, 0)"></div>
                                                    </div>
                                                </div>
                                                <div class="handle"></div>
                                                <div class="delete-name">{{item.cname}}</div>
                                            </div>
                                        </draggable>
                                    </div>
                                </div>
                            </div>
                            <div class="overflowy">
                                <div class="page-foot"
                                     @click="showFoot"
                                     :class="{ on: activeIndex == -101}"
                                     :style="pageFooterType==1?'bottom:'+(50 + pageFooterBottom)+'px':''">
                                    <footPage></footPage>
                                    <div class="delete-box"></div>
                                    <div class="handle"></div>
                                </div>
                            </div>
                            <div class="defaultData"
                                 v-if="pageId !==0">
                                <button class="data"
                                        @click="showTitle">页面设置</button>
                                <button class="data"
                                        @click="saveConfig(1,1,1)">另存模板</button>
                                <button class="data"
                                        @click="reast">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 右侧页面设置 -->
                <div class="right-box">
                    <div class="mConfig-item"
                         style="background-color:#fff;"
                         v-for="(item, key) in rConfig"
                         :key="key">
                        <component :is="item.configName"
                                   @config="config"
                                   :activeIndex="activeIndex"
                                   :num="item.num"
                                   :index="key"></component>
                    </div>
                </div>
            </div>
        </Card>
        <Modal v-model="modal"
               title="预览"
               footer-hide>
            <div>
                <div v-viewer
                     class="acea-row row-around code">
                    <div class="acea-row row-column-around row-between-wrapper">
                        <div class="QRpic"
                             ref="qrCodeUrl"></div>
                        <span class="mt10">公众号二维码</span>
                    </div>
                    <div class="acea-row row-column-around row-between-wrapper">
                        <div class="QRpic">
                            <img v-lazy="qrcodeImg" />
                        </div>
                        <span class="mt10">小程序二维码</span>
                    </div>
                </div>
            </div>
        </Modal>
    </div>
</template>

<script crossorigin='anonymous'>
    import { categoryList, diyGetInfo, diySave, setDefault, recovery, diyUpdateName, getRoutineCode } from "@/api/diy";
    import vuedraggable from "vuedraggable";
    import mPage from "@/components/mobilePage/index.js";
    import mConfig from "@/components/mobileConfig/index.js";
    import footPage from "@/components/pagesFoot";
    import { mapState } from "vuex";
    import html2canvas from 'html2canvas';
    import theme from "@/mixins/theme";
    import Setting from "@/setting";
    import QRCode from "qrcodejs2";

    export default {
        inject: ['reload'],
        name: "index.vue",
        components: {
            footPage,
            html2canvas,
            draggable: vuedraggable,
            ...mPage,
            ...mConfig,
        },
        filters: {
            filterTxt (val) {
                if (val) {
                    return (val = val.substr(0, val.length - 1));
                }
            },
        },
        computed: {
            ...mapState({
                titleTxt: (state) => state.admin.mobildConfig.pageTitle || "首页",
                nameTxt: (state) => state.admin.mobildConfig.pageName,
                showTxt: (state) => state.admin.mobildConfig.pageShow,
                colorTxt: (state) => state.admin.mobildConfig.pageColor,
                picTxt: (state) => state.admin.mobildConfig.pagePic,
                colorPickerTxt: (state) => state.admin.mobildConfig.pageColorPicker,
                tabValTxt: (state) => state.admin.mobildConfig.pageTabVal,
                picUrlTxt: (state) => state.admin.mobildConfig.pagePicUrl,
                pageFooterType: (state) => state.admin.mobildConfig.pageFooter.navConfig.tabVal,
                pageFooterBottom: (state) => state.admin.mobildConfig.pageFooter.mbConfig.val,
                defaultArrays: (state) => state.admin.mobildConfig.defaultArray
            }),
        },
        mixins: [theme],
        data () {
            return {
                BaseURL: Setting.apiBaseURL.replace(/adminapi/, ""),
                qrcodeImg: "",
                modal: false,
                clientHeight: '',//页面动态高度
                rollHeight: '',
                leftMenu: [], // 左侧菜单
                lConfig: [], // 左侧组件
                mConfig: [], // 中间组件渲染
                rConfig: [], // 右侧组件配置
                activeConfigName: "",
                propsObj: {}, // 组件传递的数据,
                activeIndex: -100, // 选中的下标
                number: 0,
                pageId: "",
                pageName: "",
                pageType: "",
                category: [],
                tabList: [
                    {
                        title: "组件库",
                        key: 0,
                    },
                    {
                        title: "页面链接",
                        key: 1,
                    },
                ],
                footActive: false,
                loading: false,
                relLoading: false,
                isSearch: false,
                isTab: false,
                isFllow: false,
                isComb: false,
                isService: false,
                visible: true,
                diyStatus: 0
            };
        },
        created () {
            this.categoryList();
            this.pageId = this.$route.query.id;
            this.pageName = this.$route.query.name;
            this.pageType = this.$route.query.type;
            this.lConfig = this.objToArr(mPage);
        },
        mounted () {
            let imgList = {
                imgList: [require('@/assets/images/foot-005.png'), require('@/assets/images/foot-006.png')],
                name: '购物车',
                link: '/pages/order_addcart/order_addcart'
            }
            this.$nextTick(() => {
                this.$store.commit("admin/mobildConfig/FOOTER", { 'title': '是否自定义', 'name': imgList });
                this.arraySort();
                if (this.pageId != 0) {
                    this.getDefaultConfig();
                } else {
                    this.showTitle();
                }
                this.clientHeight = `${document.documentElement.clientHeight}` - 65.81;//获取浏览器可视区域高度
                let H = `${document.documentElement.clientHeight}` - 180;
                this.rollHeight = H > 650 ? 650 : H;
                let that = this;
                window.onresize = function () {
                    that.clientHeight = `${document.documentElement.clientHeight}` - 65.81;
                    let H = `${document.documentElement.clientHeight}` - 180;
                    that.rollHeight = H > 650 ? 650 : H;
                }
            });
        },
        methods: {
            preview () {
                this.modal = true;
                this.creatQrCode(this.pageId, this.diyStatus);
                this.routineCode(this.pageId);
            },
            //小程序二维码
            routineCode (id) {
                getRoutineCode(id)
                    .then((res) => {
                        this.qrcodeImg = res.data.image;
                    })
                    .catch((err) => {
                        this.$Message.error(err);
                    });
            },
            //生成二维码
            creatQrCode (id, status) {
                this.$refs.qrCodeUrl.innerHTML = "";
                let url = "";
                if (status) {
                    url = `${this.BaseURL}pages/index/index`;
                } else {
                    url = `${this.BaseURL}pages/annex/special/index?id=${id}`;
                }
                var qrcode = new QRCode(this.$refs.qrCodeUrl, {
                    text: url, // 需要转换为二维码的内容
                    width: 160,
                    height: 160,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H,
                });
            },
            changName (val) {
                this.$store.commit('admin/mobildConfig/UPNAME', val.target.value)
            },
            cancel () {
                this.visible = false;
            },
            determine () {
                if (this.nameTxt.trim() == '') {
                    return this.$Message.error("请输入模板名称");
                }
                if (this.pageId == 0) {
                    this.$Message.success('修改成功');
                    return false
                }
                diyUpdateName(this.pageId, { name: this.nameTxt }).then(res => {
                    this.$Message.success(res.msg);
                }).catch(err => {
                    this.$Message.error(err.msg);
                })
                this.visible = false;
            },
            returnTap () {
                this.$Modal.confirm({
                    title: '温馨提示',
                    content: '确定离开此页面？系统可能不会保存您所做的更改。',
                    onOk: () => {
                        this.$router.push('/admin/setting/pages/devise');
                    },
                    onCancel: () => {
                        this.$Message.info('已取消');
                    }
                });
            },
            leftRemove ({ to, from, item, clone, oldIndex, newIndex }) {
                if (this.isSearch && newIndex == 0) {
                    if (item._underlying_vm_.name == "z_wechat_attention") {
                        this.isFllow = true;
                    } else {
                        this.$store.commit(
                            "admin/mobildConfig/ARRAYREAST",
                            this.mConfig[0].num
                        );
                        this.mConfig.splice(0, 1);
                    }
                }
                if (this.isFllow = true && newIndex >= 1) {
                    this.$store.commit(
                        "admin/mobildConfig/ARRAYREAST",
                        this.mConfig[0].num
                    );
                }
            },
            onMove (e) {
                if (e.relatedContext.element.name == "search_box") return false;
                if (e.relatedContext.element.name == "nav_bar") return false;
                if (e.relatedContext.element.name == "home_comb") return false;
                return true;
            },
            onCopy () {
                this.$Message.success("复制成功");
            },
            onError () {
                this.$Message.error("复制失败");
            },
            //设置默认数据
            setmoren () {
                setDefault(this.pageId).then((res) => {
                    this.$Message.success(res.msg)
                }).catch(err => {
                    this.$Message.error(err.msg)
                })
            },
            //恢复默认
            getmoren () {
                recovery(this.pageId).then((res) => {
                    this.$Message.success(res.msg)
                    this.reload();
                }).catch(err => {
                    this.$Message.error(err.msg)
                })
            },
            // 页面标题点击
            showTitle () {
                this.activeIndex = -100;
                let obj = {};
                for (var i in mConfig) {
                    if (i == "pageTitle") {
                        // this.rConfig = obj
                        obj = mConfig[i];
                        obj.configName = mConfig[i].name;
                        obj.cname = "页面设置";
                    }
                }
                let abc = obj;
                this.rConfig = [];
                this.rConfig[0] = JSON.parse(JSON.stringify(obj));
            },
            // 页面底部点击
            showFoot () {
                this.activeIndex = -101;
                let obj = {};
                for (var i in mConfig) {
                    if (i == "pageFoot") {
                        obj = mConfig[i];
                        obj.configName = mConfig[i].name;
                        obj.cname = "底部菜单";
                    }
                }
                let abc = obj;
                this.rConfig = [];
                this.rConfig[0] = JSON.parse(JSON.stringify(obj));
            },
            // 对象转数组
            objToArr (data) {
                let obj = Object.keys(data);
                let m = obj.map((key) => data[key]);
                return m;
            },
            log (evt) {
                // 中间拖拽排序
                if (evt.moved) {
                    if (evt.moved.element.name == "search_box") {
                        return this.$Message.warning("该组件禁止拖拽");
                    }
                    evt.moved.oldNum = this.mConfig[evt.moved.oldIndex].num;
                    evt.moved.newNum = this.mConfig[evt.moved.newIndex].num;
                    evt.moved.status = evt.moved.oldIndex > evt.moved.newIndex;
                    this.mConfig.forEach((el, index) => {
                        el.num = new Date().getTime() * 1000 + index;
                    });
                    evt.moved.list = this.mConfig;
                    this.rConfig = [];
                    let item = evt.moved.element;
                    let tempItem = JSON.parse(JSON.stringify(item));
                    this.rConfig.push(tempItem);
                    this.activeIndex = evt.moved.newIndex;
                    this.$store.commit("admin/mobildConfig/SETCONFIGNAME", item.name);
                    this.$store.commit("admin/mobildConfig/defaultArraySort", evt.moved);
                }
                // 从左向右拖拽排序
                if (evt.added) {
                    let data = evt.added.element;
                    let obj = {};
                    let timestamp = new Date().getTime() * 1000;
                    data.num = timestamp;
                    this.activeConfigName = data.name;
                    let tempItem = JSON.parse(JSON.stringify(data));
                    tempItem.id = "id" + tempItem.num;
                    this.mConfig[evt.added.newIndex] = tempItem;
                    this.rConfig = [];
                    this.rConfig.push(tempItem);
                    this.mConfig.forEach((el, index) => {
                        el.num = new Date().getTime() * 1000 + index;
                    });
                    evt.added.list = this.mConfig;
                    this.activeIndex = evt.added.newIndex;
                    // 保存组件名称
                    this.$store.commit("admin/mobildConfig/SETCONFIGNAME", data.name);
                    this.$store.commit("admin/mobildConfig/defaultArraySort", evt.added);
                }
            },
            cloneDog (data) {
                return {
                    ...data,
                };
            },
            //数组元素互换位置
            swapArray (arr, index1, index2) {
                arr[index1] = arr.splice(index2, 1, arr[index1])[0];
                return arr;
            },
            //点击上下移动；
            movePage (item, index, type) {
                if (type) {
                    if (index == 0) {
                        return
                    }
                } else {
                    if (index == this.mConfig.length - 1) {
                        return
                    }
                }
                if (item.name == "search_box" || item.name == "nav_bar" || item.name == "home_comb") {
                    return this.$Message.warning("该组件禁止移动");
                }
                if (type) {
                    if (this.mConfig[index - 1].name == "search_box" || this.mConfig[index - 1].name == "nav_bar" || this.mConfig[index - 1].name == "home_comb") {
                        return this.$Message.warning("搜索框或选项卡或轮播搜索必须为顶部");
                    }
                    this.swapArray(this.mConfig, index - 1, index);
                } else {
                    this.swapArray(this.mConfig, index, index + 1);
                }
                let obj = {};
                this.rConfig = [];
                obj.oldIndex = index;
                if (type) {
                    obj.newIndex = index - 1;
                } else {
                    obj.newIndex = index + 1;
                }
                this.mConfig.forEach((el, index) => {
                    el.num = new Date().getTime() * 1000 + index;
                });
                let tempItem = JSON.parse(JSON.stringify(item));
                this.rConfig.push(tempItem);
                obj.element = item;
                obj.list = this.mConfig;
                if (type) {
                    this.activeIndex = index - 1;
                } else {
                    this.activeIndex = index + 1;
                }
                this.$store.commit("admin/mobildConfig/SETCONFIGNAME", item.name);
                this.$store.commit("admin/mobildConfig/defaultArraySort", obj);
            },
            // 组件添加
            addDomCon (item, type, index) {
                if (item.name == "search_box") {
                    if (this.isSearch) return this.$Message.error("该组件只能添加一次");
                    if (this.isComb) return this.$Message.error("轮播搜索不能和搜索组件与选项卡组件同时存在");
                    this.isSearch = true;
                }
                if (item.name == "nav_bar") {
                    if (this.isTab) return this.$Message.error("该组件只能添加一次");
                    if (this.isComb) return this.$Message.error("轮播搜索不能和搜索组件与选项卡组件同时存在");
                    this.isTab = true;
                }
                if (item.name == "home_comb") {
                    if (this.isComb) return this.$Message.error("该组件只能添加一次");
                    if (this.isSearch || this.isTab) return this.$Message.error("轮播搜索不能和搜索组件与选项卡组件同时存在");
                    this.isComb = true;
                }
                if (item.name == "home_service") {
                    if (this.isService) return this.$Message.error("该组件只能添加一次");
                    this.isService = true;
                }
                let obj = {};
                let timestamp = new Date().getTime() * 1000;
                item.num = `${timestamp}`;
                item.id = `id${timestamp}`;
                this.activeConfigName = item.name;
                let tempItem = JSON.parse(JSON.stringify(item));
                if (item.name == "home_comb") {
                    this.rConfig = [];
                    this.mConfig.unshift(tempItem);
                    this.activeIndex = 0;
                    this.rConfig.push(tempItem);
                } else if (item.name == "search_box") {
                    this.rConfig = [];
                    this.mConfig.unshift(tempItem);
                    this.activeIndex = 0;
                    this.rConfig.push(tempItem);
                } else if (item.name == "nav_bar") {
                    this.rConfig = [];
                    if (this.mConfig[0] && this.mConfig[0].name === "search_box") {
                        this.mConfig.splice(1, 0, tempItem);
                        this.activeIndex = 1;
                    } else {
                        this.mConfig.splice(0, 0, tempItem);
                        this.activeIndex = 0;
                    }
                    this.rConfig.push(tempItem);
                }
                else {
                    if (type) {
                        this.rConfig = [];
                        if (this.activeIndex == 0 && this.mConfig[1] && this.mConfig[1].name == 'nav_bar') {
                            this.activeIndex = 2
                        } else {
                            this.activeIndex = this.activeIndex >= 0 ? this.activeIndex + 1 : this.mConfig.length;
                        }
                        this.mConfig.splice(this.activeIndex, 0, tempItem);
                        this.rConfig.push(tempItem);
                    } else {
                        this.mConfig.splice(index + 1, 0, tempItem);
                        this.activeIndex = index;
                    }
                }
                this.mConfig.forEach((el, index) => {
                    el.num = new Date().getTime() * 1000 + index;
                });
                // 保存组件名称
                obj.element = item;
                obj.list = this.mConfig;
                this.$store.commit("admin/mobildConfig/SETCONFIGNAME", item.name);
                this.$store.commit("admin/mobildConfig/defaultArraySort", obj);
            },
            //中间页点击添加模块；
            bindAddDom (item, type, index) {
                let i = item;
                this.lConfig.forEach(j => {
                    if (item.name == j.name) {
                        i = j
                    }
                });
                this.addDomCon(i, type, index);
            },
            //左边配置模块点击添加；
            addDom (item, type) {
                this.addDomCon(item, type);
            },
            // 点击显示相应的配置
            bindconfig (item, index) {
                this.rConfig = [];
                let tempItem = JSON.parse(JSON.stringify(item));
                this.rConfig.push(tempItem);
                this.activeIndex = index;
                this.$store.commit("admin/mobildConfig/SETCONFIGNAME", item.name);
            },
            bindHide (item) {
                let obj = this.$store.state.admin.mobildConfig.defaultArray;
                let num = this.rConfig[0].num
                obj[num].isHide = !obj[num].isHide
                this.$store.commit('admin/mobildConfig/UPDATEARR', { num: num, val: obj[num] });
            },
            // 组件删除
            bindDelete (item, key) {
                if (item.name == "search_box") {
                    this.isSearch = false;
                }
                if (item.name == "nav_bar") {
                    this.isTab = false;
                }
                if (item.name == "home_comb") {
                    this.isComb = false;
                }
                if (item.name == "home_service") {
                    this.isService = false;
                }
                this.mConfig.splice(key, 1);
                this.rConfig.splice(0, 1);
                if (this.mConfig.length != key) {
                    this.rConfig.push(this.mConfig[key]);
                } else {
                    if (this.mConfig.length) {
                        this.activeIndex = key - 1;
                        this.rConfig.push(this.mConfig[key - 1]);
                    } else {
                        this.showTitle()
                    }
                }
                // 删除第几个配置
                this.$store.commit("admin/mobildConfig/DELETEARRAY", item);
            },
            // 组件返回
            config (data) {
                let propsObj = this.propsObj;
                propsObj.data = data;
                propsObj.name = this.activeConfigName;
            },
            addSort (arr, index1, index2) {
                arr[index1] = arr.splice(index2, 1, arr[index1])[0];
                return arr;
            },
            // 数组排序
            arraySort () {
                let tempArr = [];
                let basis = {
                    title: "基础组件",
                    list: [],
                    isOpen: true,
                };
                let marketing = {
                    title: "营销组件",
                    list: [],
                    isOpen: true,
                };
                let tool = {
                    title: "工具组件",
                    list: [],
                    isOpen: true,
                };
                this.lConfig.map((el, index) => {
                    if (el.type == 0) {
                        basis.list.push(el);
                    }
                    if (el.type == 1) {
                        marketing.list.push(el);
                    }
                    if (el.type == 2) {
                        tool.list.push(el);
                    }
                });
                tempArr.push(basis, marketing, tool);
                this.leftMenu = tempArr;
            },
            diySaveDate (val, num, type, save) {
                diySave(type ? 0 : this.pageId, {
                    type: this.pageType || save,
                    value: val,
                    title: this.titleTxt,
                    name: this.nameTxt || '模板',
                    is_show: this.showTxt ? 1 : 0,
                    is_bg_color: this.colorTxt ? 1 : 0,
                    color_picker: this.colorPickerTxt,
                    bg_pic: this.picUrlTxt,
                    bg_tab_val: this.tabValTxt,
                    is_bg_pic: this.picTxt ? 1 : 0
                })
                    .then((res) => {
                        this.pageId = res.data.id;
                        this.$Message.success(res.msg);
                        let that = this;
                        if (num == 2) {
                            this.relLoading = false;
                            setTimeout(function () {
                                that.$router.push('/admin/setting/pages/devise');
                            }, 2000)
                        } else {
                            this.loading = false;
                        }
                    })
                    .catch((res) => {
                        this.relLoading = false;
                        this.loading = false;
                        this.$Message.error(res.msg);
                    });
            },
            // 保存配置
            saveConfig (num, type, save) {
                if (this.mConfig.length == 0) {
                    return this.$Message.error("暂未添加任何组件，保存失败！");
                }
                if (num == 1) {
                    this.loading = true;
                } else {
                    this.relLoading = true;
                }
                let val = this.$store.state.admin.mobildConfig.defaultArray;
                if (!this.footActive) {
                    let timestamp = new Date().getTime() * 1000;
                    val[timestamp] = this.$store.state.admin.mobildConfig.pageFooter;
                    this.footActive = true;
                }
                this.$nextTick(function () {
                    this.diySaveDate(val, num, type, save);
                })
            },
            // 获取默认配置
            getDefaultConfig () {
                diyGetInfo(this.pageId, {
                    type: 1,
                }).then(({ data }) => {
                    let obj = {};
                    let tempARR = [];
                    this.$store.commit("admin/mobildConfig/titleUpdata", data.info.title);
                    this.$store.commit("admin/mobildConfig/nameUpdata", data.info.name);
                    this.$store.commit("admin/mobildConfig/showUpdata", data.info.is_show);
                    this.$store.commit("admin/mobildConfig/colorUpdata", data.info.is_bg_color || 0);
                    this.$store.commit("admin/mobildConfig/picUpdata", data.info.is_bg_pic || 0);
                    this.$store.commit("admin/mobildConfig/pickerUpdata", data.info.color_picker || '#f5f5f5');
                    this.$store.commit("admin/mobildConfig/radioUpdata", data.info.bg_tab_val || 0);
                    this.$store.commit("admin/mobildConfig/picurlUpdata", data.info.bg_pic || '');
                    this.diyStatus = data.info.status;

                    let newArr = this.objToArr(data.info.value);

                    function sortNumber (a, b) {
                        return a.timestamp - b.timestamp;
                    }

                    newArr.sort(sortNumber);
                    newArr.map((el, index) => {
                        if (el.name == "headerSerch") {
                            this.isSearch = true;
                        }
                        if (el.name == "tabNav") {
                            this.isTab = true;
                        }
                        if (el.name == "homeComb") {
                            this.isComb = true;
                        }
                        if (el.name == "customerService") {
                            this.isService = true;
                        }
                        if (el.name == "goodList") {
                            // let storage = window.localStorage;
                            // storage.setItem(el.timestamp, el.selectConfig.activeValue);
                        }
                        el.id = "id" + el.timestamp;
                        this.lConfig.map((item, j) => {
                            if (el.name == item.defaultName) {
                                item.num = el.timestamp;
                                item.id = "id" + el.timestamp;
                                let tempItem = JSON.parse(JSON.stringify(item));
                                tempARR.push(tempItem);
                                obj[el.timestamp] = el;
                                this.mConfig.push(tempItem);
                                // 保存默认组件配置
                                this.$store.commit("admin/mobildConfig/ADDARRAY", {
                                    num: el.timestamp,
                                    val: el,
                                });
                            }
                        });
                    });

                    let objs = newArr[newArr.length - 1];

                    if (objs.name == "pageFoot") {
                        this.$store.commit("admin/mobildConfig/footPageUpdata", objs);
                    }
                    this.showTitle();
                });
            },
            categoryList () {
                categoryList((res) => {
                    this.category = res.data;
                });
            },
            // 重置
            reast () {
                if (this.pageId == 0) {
                    this.$Message.error("新增页面，无法重置");
                } else {
                    this.$Modal.confirm({
                        title: "提示",
                        content: "<p>是否重置当前页面数据</p>",
                        onOk: () => {
                            this.mConfig = [];
                            this.rConfig = [];
                            this.activeIndex = -99;
                            this.getDefaultConfig();
                        },
                        onCancel: () => {
                        },
                    });
                }
            },
        },
        beforeDestroy () {
            this.$store.commit("admin/mobildConfig/titleUpdata", "");
            this.$store.commit("admin/mobildConfig/nameUpdata", "");
            this.$store.commit("admin/mobildConfig/showUpdata", 1);
            this.$store.commit("admin/mobildConfig/colorUpdata", 0);
            this.$store.commit("admin/mobildConfig/picUpdata", 0);
            this.$store.commit("admin/mobildConfig/pickerUpdata", "#f5f5f5");
            this.$store.commit("admin/mobildConfig/radioUpdata", 0);
            this.$store.commit("admin/mobildConfig/picurlUpdata", "");
            this.$store.commit("admin/mobildConfig/SETEMPTY");
        },
        destroyed () {
            this.$store.commit("admin/mobildConfig/titleUpdata", "");
            this.$store.commit("admin/mobildConfig/nameUpdata", "");
            this.$store.commit("admin/mobildConfig/showUpdata", 1);
            this.$store.commit("admin/mobildConfig/colorUpdata", 0);
            this.$store.commit("admin/mobildConfig/picUpdata", 0);
            this.$store.commit("admin/mobildConfig/pickerUpdata", "#f5f5f5");
            this.$store.commit("admin/mobildConfig/radioUpdata", 0);
            this.$store.commit("admin/mobildConfig/picurlUpdata", "");
            this.$store.commit("admin/mobildConfig/SETEMPTY");
        },
    };
</script>

<style scoped lang="stylus">
    .code
        position relative
    .QRpic
        width 160px
        height 160px
        img
            width 100%
            height 100%
    .icon
        width 28px
        height 28px
        // vertical-align: -0.15em;
        fill currentColor
        overflow hidden
    .release.ivu-btn
        color #2D8CF0
    .buttons
        .ivu-btn
            padding 0
        .iconfont
            font-size 12px
            margin-right 2px
    .diy-page
        /deep/.ivu-page-header
            background linear-gradient(270deg, #009DFF 0%, #0550FF 100%)
            border-radius 0
        .f_return
            color #fff
            .return
                color #fff
                &::after
                    left 76px
            .iconfont
                color #fff
            .f_title
                &:hover
                    .return
                        color rgba(255, 255, 255, 0.8)
                    .iconfanhui
                        color rgba(255, 255, 255, 0.8)
                .name
                    font-size 16px
                .iconfont
                    margin-left 10px
                    color #fff
    .ysize
        background-size 100%
    .fullsize
        background-size 100% 100%
    .repeat
        background-repeat repeat
    .noRepeat
        background-repeat no-repeat
    .wrapper-con
        flex 1
        background #f0f2f5
        display flex
        justify-content center
        padding-top 20px
        height 100%
    .defaultData
        /* margin-left 20px; */
        cursor pointer
        position absolute
        left 50%
        margin-left 245px
        .data
            margin-top 20px
            color #666
            background-color #fff
            width 80px
            text-align center
            height 32px
            line-height 32px
            border-radius 3px
            font-size 13px
            border 1px solid #fff
            border-radius 3px
            box-shadow 0px 1px 6px 0px rgba(0, 0, 0, 0.03)
            display block
        .data:hover, .data:focus
            background-color #fff
            color #1890FF
            border 1px solid rgba(24, 144, 255, 0.5)
    .overflowy
        margin-right 4px
        .picture
            width 379px
            height 20px
            margin 0 auto
            background-color #fff
    .bnt
        width 80px !important
        &:hover
            border-color rgba(255, 255, 255, 0.8)
            color rgba(255, 255, 255, 0.8)
    .w-74
        width 74px !important
    .w-80
        width 80px !important
    /* 定义滑块 内阴影+圆角 */
    ::-webkit-scrollbar-thumb
        -webkit-box-shadow inset 0 0 6px #fff
        display none
    .left:hover::-webkit-scrollbar-thumb, .right-box:hover::-webkit-scrollbar-thumb
        display block
    .contxt
        display flex
        flex-direction column
        overflow hidden
        height 100%
    .contxt:hover ::-webkit-scrollbar-thumb
        display block
    ::-webkit-scrollbar
        width 4px !important /* 对垂直流动条有效 */
    .scrollCon
        overflow-y scroll
        overflow-x hidden
    .scroll-box .position
        display block !important
        height 40px
        text-align center
        line-height 40px
        border 1px dashed #1890ff
        color #1890ff
        background-color #edf4fb
    .scroll-box .conter
        display none !important
    .dragClass
        background-color #fff
    .ivu-mt
        display flex
        justify-content space-between
    .iconfont-diy
        font-size 24px
        color #1890ff
    .diy-wrapper
        max-width 100%
        min-width 1100px
        display flex
        justify-content space-between
        /* height: 84.5vh; */
        .left
            min-width 300px
            max-width 300px
            /* border 1px solid #DDDDDD */
            border-radius 4px
            height 100%
            .title-bar
                display flex
                color #333
                border-bottom 1px solid #eee
                border-radius 4px
                cursor pointer
                .title-item
                    display flex
                    align-items center
                    justify-content center
                    flex 1
                    height 45px
                    &.on
                        color #1890FF
                        font-size 14px
                        border-bottom 1px solid #1890FF
            .wrapper
                padding 24px 20px
                overflow-y scroll
                -webkit-overflow-scrolling touch
                .list
                    &~.list
                        .tips
                            margin-top 24px
                .tips
                    display flex
                    justify-content space-between
                    padding-bottom 15px
                    font-size 13px
                    color #000
                    cursor pointer
                    .iconfont
                        font-size 12px
                    .ivu-icon
                        color #000
            .link-item
                padding 10px
                border-bottom 1px solid #F5F5F5
                font-size 12px
                color #323232
                .name
                    font-size 14px
                    color #1890FF
                .link-txt
                    margin-top 2px
                    word-break break-all
                .params
                    margin-top 5px
                    color #1CBE6B
                    word-break break-all
                    .txt
                        color #323232
                    span
                        &:last-child i
                            display none
                            color red
                .lable
                    display flex
                    margin-top 5px
                    color #999
                    p
                        flex 1
                        word-break break-all
                    button
                        margin-left 30px
                        width 38px
            .dragArea.list-group
                display flex
                flex-wrap wrap
                .list-group-item
                    display flex
                    flex-direction column
                    align-items center
                    justify-content center
                    width 74px
                    height 66px
                    margin-right 17px
                    margin-bottom 10px
                    font-size 12px
                    color #666
                    cursor pointer
                    border-radius 5px
                    text-align center
                    &:hover
                        box-shadow 0 0 5px 0 rgba(24, 144, 255, 0.3)
                        border-right 5px
                    &:nth-child(3n)
                        margin-right 0
        .content
            position relative
            height 100%
            width 100%
            .page-foot
                position relative
                width 379px
                margin 0 auto 20px auto
                .delete-box
                    display none
                    position absolute
                    left -2px
                    top 0
                    width 383px
                    height 100%
                    border 2px dashed #1890ff
                    padding 10px 0
                &:hover, &.on
                    /* cursor: move; */
                    .delete-box
                        /* display: block; */
                &.on
                    cursor move
                    .delete-box
                        display block
                        border 2px solid #1890ff
                        box-shadow 0 0 10px 0 rgba(24, 144, 255, 0.3)
            .page-title
                position relative
                height 35px
                line-height 35px
                background #fff
                font-size 15px
                color #333333
                text-align center
                width 379px
                margin 0 auto
                .delete-box
                    display none
                    position absolute
                    left -2px
                    top 0
                    width 383px
                    height 100%
                    border 2px dashed #1890ff
                    padding 10px 0
                    span
                        position absolute
                        right 0
                        bottom 0
                        width 32px
                        height 16px
                        line-height 16px
                        display inline-block
                        text-align center
                        font-size 10px
                        color #fff
                        background rgba(0, 0, 0, 0.4)
                        margin-left 2px
                        cursor pointer
                        z-index 11
                &:hover, &.on
                    /* cursor: move; */
                    .delete-box
                        /* display: block; */
                &.on
                    cursor move
                    .delete-box
                        display block
                        border 2px solid #1890ff
                        box-shadow 0 0 10px 0 rgba(24, 144, 255, 0.3)
            .scroll-box
                flex 1
                background-color #fff
                width 379px
                margin 0 auto
                padding-top 1px
            .dragArea.list-group
                width 100%
                height 100%
                .mConfig-item
                    position relative
                    cursor move
                    &.hide
                        &::before
                            position absolute
                            content '已隐藏'
                            background rgba(0, 0, 0, 0.5)
                            width 100%
                            height 100%
                            z-index 99
                            color #fff
                            display flex
                            align-items center
                            justify-content center
                    .delete-name
                        position absolute
                        top 0
                        background #fff
                        left -100px
                        width 86px
                        height 32px
                        text-align center
                        line-height 32px
                        font-size 13px
                        color #666
                        border-radius 3px
                        &::before
                            content ''
                            position absolute
                            width 10px
                            height 10px
                            background #fff
                            transform rotate(45deg)
                            top 50%
                            right -5px
                            margin-top -5px
                    .delete-box
                        display none
                        position absolute
                        left -2px
                        top 0
                        width 383px
                        height 100%
                        border 2px dashed #1890ff
                        /* padding: 10px 0; */
                        .handleType
                            position absolute
                            right -43px
                            top 0
                            width 36px
                            height 173px
                            border-radius 4px
                            background-color #1890ff
                            cursor pointer
                            color #fff
                            font-weight bold
                            text-align center
                            padding 4px 0
                            .iconfont
                                padding 5px 0
                                font-weight 500
                                &.on
                                    opacity 0.4
                    &.on
                        cursor move
                        .delete-box
                            display block
                            border 2px solid #1890ff
                            box-shadow 0 0 10px 0 rgba(24, 144, 255, 0.3)
        .right-box
            max-width 400px
            min-width 400px
            height 100%
            border-radius 4px
            overflow auto
            -webkit-overflow-scrolling touch
            .title-bar
                width 100%
                height 45px
                line-height 45px
                padding-left 24px
                color #000
                border-radius 4px
                border-bottom 1px solid #eee
                font-size 14px
        ::-webkit-scrollbar
            width 6px
            background-color transparent
        ::-webkit-scrollbar-track
            border-radius 10px
        ::-webkit-scrollbar-thumb
            background-color #bfc1c4
    .foot-box
        position relative
        display flex
        align-items center
        justify-content center
        height 80px
        background #fff
        box-shadow 0px -2px 4px 0px rgba(0, 0, 0, 0.03)
        button
            width 100px
            height 32px
            font-size 13px
            &:first-child
                margin-right 20px
    /deep/ .ivu-scroll-loader
        display none
    /deep/ .ivu-card-body
        width 100%
</style>
