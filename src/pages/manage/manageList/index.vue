<template>
    <!-- 商品-商品分类 -->
    <div class="article-manager">
        <Card :bordered="false"
              dis-hover
              class="ivu-mt"
              :padding="0">
            <div class="new_card_pd">
                <!-- 筛选条件 -->
                <Form ref="artFrom"
                      :model="artFrom"
                      :label-width="labelWidth"
                      inline
                      :label-position="labelPosition"
                      @submit.native.prevent>
                    <FormItem label="就诊人名称"
                              label-for="status2">
                        <Input placeholder="请输入就诊人名称"
                               clearable
                               v-model="artFrom.patient_name"
                               class="input-add mr14" />

                    </FormItem>
                    <FormItem label="预约时间">
                        <DatePicker type="date"
                                    placeholder="请选择预约时间"
                                    v-model="artFrom.treatment_date"
                                    style="width: 200px" />
                    </FormItem>

                    <FormItem label="状态">
                        <Select v-model="artFrom.status"
                                style="width: 120px">
                            <Option v-for="item in statusOptions"
                                    :key="item.value"
                                    :value="item.value">{{ item.label }}</Option>
                        </Select>
                    </FormItem>

                    <FormItem label="号别">
                        <Select v-model="artFrom.order_level"
                                style="width: 120px">
                            <Option v-for="item in orderLevelOptions"
                                    :key="item.value"
                                    :value="item.value">{{ item.label  }}</Option>
                        </Select>
                    </FormItem>

                    <FormItem label="预约科室">
                        <Select v-model="artFrom.dept_id"
                                style="width: 150px">
                            <Option v-for="item in deptOptions"
                                    :key="item.dept_id"
                                    :value="item.dept_id">{{ item.dept_name }}</Option>
                        </Select>
                    </FormItem>

                    <Button type="primary"
                            @click="userSearchs()">查询</Button>
                    <Button class="ResetSearch"
                            style="margin-left: 20px;"
                            @click="reset('artFrom')">重置</Button>
                </Form>
            </div>
        </Card>
        <Card :bordered="false"
              dis-hover
              class="ivu-mt">
            <!-- 商品分类表格 -->
            <vxe-table :data="tableData"
                       ref="xTable"
                       class="ivu-mt"
                       highlight-hover-row
                       :loading="loading"
                       header-row-class-name="false">
                <vxe-table-column field="patient_name"
                                  width="120"
                                  title="就诊人"></vxe-table-column>
                <vxe-table-column field="id_card"
                                  title="身份证号"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="link_phone"
                                  title="手机号"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="dept_name"
                                  title="预约科室"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="registration_time"
                                  title="预约时间"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="order_level_text"
                                  title="号别"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="pay_type_text"
                                  title="支付方式"
                                  tooltip="true">
                    <template v-slot="{ row }">
                        <span :style="{ color: getPayColor(row.pay_type_text) }">
                            {{ row.pay_type_text }}
                        </span>
                    </template>
                </vxe-table-column>
                <vxe-table-column field="status_text"
                                  title="状态"
                                  tooltip="true"></vxe-table-column>
                <vxe-table-column field="date"
                                  title="操作">
                    <template v-slot="{ row, index }">
                        <Divider type="vertical" />
                        <a @click="signal(row.id)">签到</a>
                        <a @click="Withdrawals(row.id)"
                           style="margin-left: 20px ;">退号</a>
                        <a @click="showDetailDialog(row.id)"
                           style="margin-left:20px">详情</a>

                    </template>
                </vxe-table-column>
            </vxe-table>
            <div class="ivu-mt ivu-text-right">
                <Page :total="total"
                      :current="artFrom.page"
                      :page-size="artFrom.limit"
                      @on-change="changePatientPages"
                      show-elevator
                      show-total />
            </div>
        </Card>
        <Modal v-model="detailModal"
               title="科室详情"
               :mask-closable="false"
               width="600">
            <Form :label-width="100">
                <FormItem label="状态"
                          style="font-size: 20px;font-weight: bold;">
                    <span>{{ detailData.status_text }}</span>
                </FormItem>
                <FormItem label="预约科室：">
                    <span>{{ detailData.dept_name }}</span>
                </FormItem>
                <FormItem label="预约时间：">
                    <span>{{ detailData.treatment_date }}</span>
                </FormItem>
                <FormItem label="预约时段：">
                    <span>{{ detailData.start_period }}-{{ detailData.end_period }}</span>
                </FormItem>
                <FormItem label="号别：">
                    <span>{{ detailData.order_level_text}}</span>
                </FormItem>
                <FormItem label="挂号费：">
                    <span>{{ detailData.recei_money}}</span>
                </FormItem>
                <FormItem label="就诊人：">
                    <span>{{  detailData.patient_name}}</span>
                </FormItem>
                <FormItem label="身份证号：">
                    <span>{{  detailData.id_card}}</span>
                </FormItem>
                <FormItem label="手机号：">
                    <span>{{  detailData.link_phone}}</span>
                </FormItem>
                <FormItem label="是否本人:">
                    <span>{{  detailData.is_self}}</span>
                </FormItem>
                <FormItem label="联系人姓名:">
                    <span>{{  detailData.link_name}}</span>
                </FormItem>
                <FormItem label="联系人证件号:">
                    <span>{{  detailData.link_number}}</span>
                </FormItem>
                <FormItem label="联系人电话:">
                    <span>{{  detailData.link_phone}}</span>
                </FormItem>
                <FormItem label="症状描述：">
                    <span>{{  detailData.desc || '无' }}</span>
                </FormItem>
            </Form>

            <div slot="footer">
                <Button @click="detailModal=false">关闭</Button>
            </div>
        </Modal>

    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import { registeredOrder, registeredoperate, registereddetail, orderlevel, deptList, orderstatusList } from "@/api/user";

    export default {
        name: 'product_productClassify',
        data () {
            return {
                grid: {
                    xl: 7,
                    lg: 7,
                    md: 12,
                    sm: 24,
                    xs: 24
                },
                detailModal: false,
                detailData: [],
                loading: false,
                artFrom: {
                    page: 1,
                    patient_name: '',
                    limit: 10,
                    treatment_date: '',
                    dept_id: '',
                    order_level: '',
                    status: '',
                },
                total: 0,
                tableData: [],
                orderLevelOptions: [],
                deptOptions: [],
                statusOptions: []


            }
        },
        computed: {
            ...mapState('admin/layout', [
                'isMobile'
            ]),
            ...mapState('admin/userLevel', [
                'categoryId'
            ]),
            labelWidth () {
                return this.isMobile ? undefined : 96;
            },
            labelPosition () {
                return this.isMobile ? 'top' : 'right';
            },



        },
        mounted () {
            this.getList();
            this.selectorderlevel()
            this.selectdeptList()
            this.orderstatus()
        },
        methods: {
            getPayColor (text) {
                if (text === '微信支付') return '#09bb07';   // 红色
                if (text === '余额支付') return '#e64340';   // 绿色
                return '#333';                                // 默认色
            },
            selectorderlevel () {

                orderlevel().then(res => {
                    // 转换成 Select 可用的格式
                    this.orderLevelOptions = res.data.map((item, index) => ({
                        label: item,
                        value: index + 1 // 或者 item 本身作为 value，视后端要求
                    }));
                })

            },
            selectdeptList () {
                deptList().then(res => {
                    this.deptOptions = res.data
                })
            },
            orderstatus () {
                orderstatusList().then(res => {
                    this.statusOptions = Object.entries(res.data).map(([value, label]) => ({
                        label,
                        value: Number(value)
                    }));
                    console.log(this.statusOptions, '999')
                })
            },
            del (id) {
                this.$Modal.confirm({
                    title: '确认删除',
                    content: '确定要删除该科室吗？此操作不可恢复！',
                    okText: '确定',
                    cancelText: '取消',
                    onOk: () => {
                        deldepartment({ id }).then(res => {
                            if (res.status === 200) {
                                this.$Message.success('删除成功');
                                this.getList();           // 刷新列表
                            } else {
                                this.$Message.error(res.msg || '删除失败');
                            }
                        }).catch(err => {
                            this.$Message.error(err.msg || '删除失败');
                        });
                    },
                    onCancel: () => {
                        // 用户点击取消不做任何操作
                    }
                });
            },
            openAddModal () {
                this.addForm = {
                    dept_name: '',
                    desc: '',
                    image: '',
                    banners: []
                };
                this.addModal = true;
            },

            changePatientPages (page) {
                this.artFrom.page = page;
                this.getList();
            },
            showDetailDialog (id) {
                registereddetail({ id: id }).then(res => {
                    if (res.status === 200) {
                        this.detailData = res.data;
                        this.detailModal = true;
                    } else {
                        this.$Message.error(res.msg || '获取详情失败');
                    }
                }).catch(err => {
                    this.$Message.error(err.msg || '获取详情失败');
                });
            },
            // 列表
            getList () {
                this.loading = true;

                // 克隆一份 artFrom，避免污染原数据
                const params = { ...this.artFrom };

                // 格式化日期
                if (params.treatment_date) {
                    const date = new Date(params.treatment_date);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    params.treatment_date = `${year}-${month}-${day}`;
                }

                registeredOrder(params).then(res => {
                    this.tableData = res.data.list;
                    this.total = res.data.count;
                    this.loading = false;
                }).catch(err => {
                    this.loading = false;
                    this.$Message.error(err.msg);
                });
            },
            pageChange (index) {
                this.artFrom.page = index;
                this.getList();
            },
            reset () {
                this.artFrom.patient_name = ''
                this.artFrom.treatment_date = ''
                this.artFrom.dept_id = ''
                this.artFrom.order_level = ''
                this.artFrom.status = ''
                this.getList(this.artFrom);
            },
            // 表格搜索
            userSearchs () {
                this.artFrom.page = 1;
                this.getList();
            },
            /**
             * 
             */
            signal (id) {
                this.$Modal.confirm({
                    title: '确认签到',
                    content: '请确认患者是否到院，签到后将无法进行退号！',
                    okText: '确定',
                    cancelText: '取消',
                    onOk: () => {
                        const data = {
                            id: id,
                            operation: 1
                        }
                        registeredoperate(data).then(res => {
                            if (res.status === 200) {
                                this.$Message.success('签到成功');
                                this.getList();           // 刷新列表
                            } else {
                                this.$Message.error(res.msg || '签到失败');
                            }
                        }).catch(err => {
                            this.$Message.error(err.msg || '签到失败');
                        });
                    },
                    onCancel: () => {
                        // 用户点击取消不做任何操作
                    }
                });
            },
            Withdrawals (id) {
                this.$Modal.confirm({
                    title: '确认退号',
                    content: '请确认患者是否退号，退号后挂号费将原路退回！',
                    okText: '确定',
                    cancelText: '取消',
                    onOk: () => {
                        const data = {
                            id: id,
                            operation: 2
                        }
                        registeredoperate(data).then(res => {
                            if (res.status === 200) {
                                this.$Message.success('退号成功');
                                this.getList();           // 刷新列表
                            } else {
                                this.$Message.error(res.msg || '退号失败');
                            }
                        }).catch(err => {
                            this.$Message.error(err.msg || '退号失败');
                        });
                    },
                    onCancel: () => {
                        // 用户点击取消不做任何操作
                    }
                });
            }


        }
    }
</script>
<style scoped lang="stylus">
    .treeSel >>>.ivu-select-dropdown-list
        padding 0 10px !important
        box-sizing border-box
    .tabBox_img
        width 36px
        height 36px
        border-radius 4px
        cursor pointer
        img
            width 100%
            height 100%
    /deep/.ivu-input
        font-size 14px !important
    .project-time-row
        display flex
        margin-bottom 10px
    .add-project-button
        margin-left 90px
        margin-bottom 30px
        border solid 1px #2D8CF0
        color #2D8CF0
        line-hight 10px
        .suffix
            position absolute
            right 10px
            top 50%
            transform translateY(-50%)
            font-size 14px
            color #1A1A1A
            pointer-events none
</style>
<style lang="less" scoped>
    .container {
        padding: 20px;
        background-color: #f5f5f5;
        height: 100%;
    }

    .gauge-canvas {
        width: 230px;
        height: 135px;
    }

    .report-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative; /* 添加 position 属性 */
    }

    .report-cards {
        height: 480px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: relative; /* 添加 position 属性 */
        z-index: 1; /* 设置 z-index 为 1，使其位于下层 */
    }

    .report-title {
        color: white;
        padding: 15px;
        text-align: left;
        font-size: 30px;
        font-weight: Semibold;
    }

    .report-subtitle {
        margin-top: 30px;
        color: white;
        padding: 10px;
        font-size: 14px;
        width: 150px;
        height: 40px;
        margin-left: 20px;
        text-align: left;
        border-radius: 10px;
        background-color: #1272cb; /* 基础背景色 */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5); /* 添加阴影效果 */
    }

    .score-container {
        position: absolute;
        top: 60px;
        right: -40px;
    }

    .score-gauge {
        width: 120px;
        height: 120px;
    }

    .score-value {
        font-size: 24px;
        font-weight: bold;
        margin-left: 10px;
    }

    .report-content {
        padding: 20px;
        background-color: #eff8ff;
        border-radius: 10px;
        position: absolute;
        top: 20px;
        width: calc(128% - 40px);
        z-index: 2; /* 设置 z-index 为 2，使其位于上层 */
        margin-top: 220px;
        margin-left: 20px;
    }

    .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .section-titleBBB {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-left: 20px;
    }

    .section-text {
        font-size: 16px;
        font-weight: bold;
        color: #4a90e2;
    }

    .report-details {
        line-height: 1.6;
        color: #666;
    }

    .body-analysis {
        padding: 20px;
        // border-top: 1px solid #eee;
    }

    .body-analysisS {
        padding: 10px 0px 0px 40px;
        background-size: cover;
        background-position: center;
        position: relative; /* 添加 position 属性 */
        background-color: #f0f8ff;
        // border-radius: 10px;
        height: 40px; /* 设置背景图的高度 */
    }

    .analysis-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .analysis-titles {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        z-index: 2; /* 确保文字显示在背景图的上层 */
    }

    .analysis-text {
        font-size: 16px;
        font-weight: bold;
        color: #4a90e2;
    }

    .analysis-texts {
        font-size: 16px;
        font-weight: bold;
        color: white;
    }

    .body-outline {
        position: relative;
        height: 400px;
        background-color: #f0f8ff;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .body-icon {
        width: 200px;
        height: 400px;
        border-radius: 100px;
        position: relative;
    }

    .body-labels {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .label {
        position: absolute;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        color: #333;
    }

    /* 疾病部分样式 */
    .disease-section {
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        margin-left: 20px;
        margin-right: 20px;
        // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* 疾病部分样式 */
    .disease-sectionAAA {
        padding: 10px;
        background-color: white;
        border-radius: 10px;
        margin-right: 20px;
    }

    .disease-details {
        line-height: 1.6;
        color: #444;
        padding: 10px;
        background-color: white;
        border-radius: 8px;
        // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .disease-text {
        margin-bottom: 8px;
        font-size: 14px;
    }

    .Adisease-section {
        background-color: white;
        border-radius: 10px;
        margin-left: 20px;
        margin-right: 20px;
    }

    .image-container {
        width: 300px;
        height: 300px;
        border-radius: 10px;
        overflow: hidden;
        margin: 20px 0;
        position: relative;
    }

    .tongue-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }
    .background {
        width: 62%;
        height: 70%;
        background-position: center; /* 背景图片居中 */
        background-repeat: no-repeat; /* 不重复背景图片 */
        background-size: cover; /* 背景图片覆盖整个容器 */
        position: absolute;
        margin-top: -88%;
        margin-left: 19%;
        border-radius: 15px;
        z-index: 2;
    }
</style>

