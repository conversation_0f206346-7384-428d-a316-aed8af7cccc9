<template>
  <!-- 积分-积分分类 -->
  <div>
    <Card :bordered="false" dis-hover class="ivu-mt" :padding="0">
      <div class="new_card_pd">
        <!-- 筛选条件 -->
        <Form
            ref="sortFrom"
            inline
            :model="sortFrom"
            :label-width="labelWidth"
            :label-position="labelPosition"
            @submit.native.prevent
        >
          <Row :gutter="24" type="flex" justify="end">
            <Col span="24">
              <FormItem label="分类状态："  label-for="is_show">
                <Select v-model="sortFrom.is_show" placeholder="请选择" clearable  @on-change="sortSearchs"  class="input-add">
                  <Option value="1">显示</Option>
                  <Option value="0">隐藏</Option>
                </Select>
              </FormItem>
              <FormItem label="分类名称：">
                <Input
                    v-model="sortFrom.name"
                    placeholder="请输入分类名称"
                    class="input-add"
                ></Input>
                <Button @click="sortSearchs" type="primary">查询</Button>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>
    </Card>

    <Card :bordered="false" dis-hover class="ivu-mt">
      <!-- 相关操作 -->
      <Row type="flex">
        <Col v-bind="grid">
          <Button type="primary" @click="add"
          >添加积分分类</Button
          >
        </Col>
      </Row>
      <!-- 积分表格 -->
      <Table
          :columns="columns1"
          :data="list"
          ref="table"
          class="mt25"
          :loading="loading"
          highlight-row
          no-userFrom-text="暂无数据"
          no-filtered-userFrom-text="暂无筛选结果"
      >
        <template slot-scope="{ row, index }" slot="range">{{row.integral_min}} - {{row.integral_max}}</template>
        <template slot-scope="{ row, index }" slot="status">
          <i-switch v-model="row.is_show" :value="row.is_show" :true-value="1" :false-value="0" @on-change="onchangeIsShow(row)" size="large">
            <span slot="open">显示</span>
            <span slot="close">隐藏</span>
          </i-switch>
        </template>
        <template slot-scope="{ row, index }" slot="action">
          <a @click="edit(row.id)">编辑</a>
          <Divider type="vertical" />
          <a @click="del(row, '删除单位', index)">删除</a>
        </template>
      </Table>
      <div class="acea-row row-right page">
        <Page
            :total="total"
            show-elevator
            show-total
            @on-change="pageChange"
            :page-size="sortFrom.limit"
        />
      </div>
    </Card>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { integralCategory, integralCreate, integralCategoryEdit, categorySetShow } from "@/api/marketing";
export default {
  name: "user_group",
  data() {
    return {
      grid: {
        xl: 7,
        lg: 7,
        md: 12,
        sm: 24,
        xs: 24,
      },
      loading: false,
      columns1: [
        {
          title: "ID",
          key: "id",
          width: 80,
        },
        {
          title: "分类名称",
          key: "name",
          minWidth: 100,
        },
        {
          title: "积分范围",
          slot: "range",
          minWidth: 100,
        },
        {
          title: "状态",
          slot: "status",
          minWidth: 100,
        },
        {
          title: "排序",
          key: "sort",
          minWidth: 100,
        },
        {
          title: "操作",
          slot: "action",
          width: 120,
        },
      ],
      sortFrom: {
        page: 1,
        limit: 15,
        name: "",
        is_show: '',
      },
      list: [],
      total: 0,
    };
  },
  computed: {
    ...mapState("admin/layout", ["isMobile"]),
    labelWidth() {
      return this.isMobile ? undefined : 85;
    },
    labelPosition() {
      return this.isMobile ? "top" : "right";
    },
  },
  created() {
    this.getList();
  },
  methods: {
    sortSearchs() {
      this.sortFrom.page = 1;
      this.list = [];
      this.getList();
    },
    // 修改状态
    onchangeIsShow (row) {
      let data = {
        id: row.id,
        is_show: row.is_show
      }
      categorySetShow(data).then(async res => {
        this.$Message.success(res.msg);
        this.getList()
      }).catch(res => {
        this.$Message.error(res.msg);
      })
    },
    // 添加
    add() {
      this.$modalForm(integralCreate()).then(() => this.getList());
    },
    // 分类列表
    getList() {
      this.loading = true;
      integralCategory(this.sortFrom)
          .then((res) => {
            let data = res.data;
            this.list = data.list;
            this.total = data.count;
            this.loading = false;
          })
          .catch((err) => {
            this.loading = false;
            this.$Message.error(err.msg);
          });
    },
    pageChange(index) {
      this.sortFrom.page = index;
      this.getList();
    },
    //修改
    edit(id) {
      this.$modalForm(integralCategoryEdit(id)).then(() => this.getList());
    },
    // 删除
    del(row, tit, num) {
      let delfromData = {
        title: tit,
        num: num,
        url: `/marketing/integral/category/${row.id}`,
        method: "DELETE",
        ids: "",
      };
      this.$modalSure(delfromData)
          .then((res) => {
            this.$Message.success(res.msg);
            this.list.splice(num, 1);
            if (!this.list.length) {
              this.sortFrom.page =
                  this.sortFrom.page == 1 ? 1 : this.sortFrom.page - 1;
            }
            this.getList();
          })
          .catch((err) => {
            this.$Message.error(err.msg);
          });
    },
  },
};
</script>

<style scoped lang="stylus">
.input-add {
  width: 250px;
  margin-right:14px;
}
</style>