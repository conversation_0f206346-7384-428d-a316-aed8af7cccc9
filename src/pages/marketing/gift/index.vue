<template>
    <!-- 积分-积分分类 -->
    <div>
        <!-- 筛选+列表卡片 -->
        <Card :bordered="false"
              dis-hover
              class="ivu-mt"
              :padding="0">
            <div class="new_card_pd">
                <Form ref="sortFrom"
                      inline
                      :model="sortFrom"
                      :label-width="labelWidth"
                      :label-position="labelPosition"
                      @submit.native.prevent>
                    <Row :gutter="24"
                         type="flex"
                         justify="end">
                        <Col span="24">
                        <FormItem label="礼品卡名称：">
                            <Input v-model="sortFrom.name"
                                   clearable
                                   placeholder="请输入礼品卡名称"
                                   class="input-add" />
                        </FormItem>
                        <FormItem label="商品名称：">
                            <Input v-model="sortFrom.goods_name"
                                   placeholder="请输入商品名称"
                                   clearable
                                   class="input-add" />
                            <Button @click="sortSearchs"
                                    type="primary">查询</Button>
                        </FormItem>
                        </Col>
                    </Row>
                </Form>
            </div>
        </Card>

        <Card dis-hover
              class="ivu-mt">
            <!-- 添加按钮 -->
            <Row type="flex">
                <Col v-bind="grid">
                <Button type="primary"
                        @click="openAddModal">添加礼品卡</Button>
                </Col>
            </Row>

            <!-- 礼品卡表格 -->
            <Table :columns="columns1"
                   :data="list"
                   ref="table"
                   class="mt25"
                   :loading="loading"
                   highlight-row
                   no-userFrom-text="暂无数据"
                   no-filtered-userFrom-text="暂无筛选结果">
                <template slot-scope="{ row }"
                          slot="status">
                    <i-switch :value="+row.status"
                              :true-value="1"
                              :false-value="0"
                              @on-change="val => onchangeIsShow(row, val)"
                              size="large">
                        <span slot="open">显示</span>
                        <span slot="close">隐藏</span>
                    </i-switch>
                </template>
                <template slot-scope="{ row, index }"
                          slot="action">
                    <Button @click="detail(row,index)"
                            type="primary">查看</Button>
                </template>
            </Table>

            <div class="acea-row row-right page">
                <Page :total="total"
                      show-elevator
                      show-total
                      @on-change="pageChange"
                      :page-size="sortFrom.limit" />
            </div>
        </Card>

        <!-- 弹窗：仅新增礼品卡 -->
        <Modal v-model="modal1"
               title="添加礼品卡"
               width="500">
            <Form ref="form"
                  :model="form"
                  :rules="rules"
                  :label-width="90">
                <FormItem label="礼品卡名称"
                          prop="name">
                    <Input v-model="form.name"
                           placeholder="请输入礼品卡名称"
                           :maxlength="100" />
                </FormItem>

                <FormItem label="制卡数量"
                          prop="num">
                    <InputNumber v-model="form.num"
                                 :min="1"
                                 :precision="0"
                                 style="width:100%" />
                </FormItem>

                <!-- 选择商品 -->
                <FormItem label="选择商品">
                    <div v-if="form.goods.id"
                         class="selected-goods">
                        <img :src="form.goods.image" />
                        <span>{{ form.goods.name }}</span>
                        <Button size="small"
                                type="primary"
                                @click="openGoodsModal">更换</Button>
                    </div>
                    <Button v-else
                            type="dashed"
                            long
                            @click="openGoodsModal">请选择商品</Button>
                </FormItem>

                <FormItem label="是否启用">
                    <i-switch v-model="form.is_show"
                              :true-value="1"
                              :false-value="0">
                        <span slot="open">启用</span>
                        <span slot="close">禁用</span>
                    </i-switch>
                </FormItem>
            </Form>

            <div slot="footer">
                <Button @click="closeModal1">取消</Button>
                <Button type="primary"
                        @click="handleSubmit()">确定</Button>
            </div>
        </Modal>

        <!-- 商品列表弹窗 -->
        <Modal v-model="modal2"
               title="选择商品"
               width="1000"
               footer-hide>
            <Form inline
                  @submit.native.prevent>
                <FormItem label="商品搜索："
                          :label-width="100">
                    <Input v-model="goodsSearch.keyword"
                           style="width:200px"
                           clearable
                           placeholder="请输入商品名称/关键字/编号" />
                </FormItem>
                <Button type="primary"
                        @click="getGoodsList">查询</Button>
            </Form>

            <Table :columns="goodsColumns"
                   :data="goodsList"
                   class="mt15"
                   max-height="400">
                <template slot-scope="{ row }"
                          slot="thumb">
                    <img :src="row.image"
                         style="width:40px;height:40px;object-fit:cover;">
                </template>
                <template slot-scope="{ row }"
                          slot="action">
                    <Button type="primary"
                            size="small"
                            @click="selectGoods(row)">选择</Button>
                </template>
            </Table>

            <div class="acea-row row-right page mt15">
                <Page :total="goodsTotal"
                      :page-size="goodsSearch.limit"
                      @on-change="goodsPageChange"
                      show-elevator
                      show-total />
            </div>
        </Modal>

        <!-- 查看礼品卡弹窗 -->
        <Modal v-model="viewModal"
               title="查看礼品卡"
               width="80%"
               footer-hide>
            <Form inline
                  :label-width="100">
                <FormItem label="使用人信息：">
                    <Input v-model="viewQuery.use_keyword"
                           placeholder="用户名/电话/ID"
                           clearable
                           style="width:130px" />
                </FormItem>
                <FormItem label="分销人信息：">
                    <Input v-model="viewQuery.dis_keyword"
                           placeholder="用户名/电话/ID"
                           clearable
                           style="width:150px" />
                </FormItem>
                <FormItem label="卡号：">
                    <Input v-model="viewQuery.card_number"
                           placeholder="请输入卡号"
                           clearable
                           style="width:150px" />
                </FormItem>
                <FormItem label="状态：">
                    <Select v-model="viewQuery.status"
                            clearable
                            style="width:120px">
                        <Option :value="0">未使用</Option>
                        <Option :value="1">已使用</Option>
                    </Select>
                </FormItem>
                <Button type="primary"
                        @click="getViewList"
                        style="margin-left:20px;">查询</Button>
                <Button type="primary"
                        @click="addViewList"
                        style="margin-left:20px;">新增</Button>
                <Button type="success"
                        @click="exportData"
                        style="margin-left:20px;">导出</Button>
            </Form>

            <Table :columns="viewColumns"
                   :data="viewList"
                   class="mt15" />
            <div class="acea-row row-right page mt15">
                <Page :total="viewTotal"
                      :page-size="viewQuery.limit"
                      @on-change="viewPageChange"
                      show-elevator
                      show-total />
            </div>
        </Modal>
        <!-- 新增制卡弹窗 -->
        <Modal v-model="addCardModal"
               title="新增制卡"
               width="400"
               :mask-closable="false">
            <Form ref="addCardForm"
                  :model="addCardForm"
                  :rules="addCardRules"
                  label-width="120">
                <FormItem label="新增制卡数量："
                          prop="total_num">
                    <InputNumber v-model="addCardForm.total_num"
                                 :min="1"
                                 :precision="0"
                                 style="width: 100%;" />
                </FormItem>
            </Form>

            <div slot="footer">
                <Button @click="addCardModal = false">取消</Button>
                <Button type="primary"
                        :loading="loadingSave"
                        @click="handleAddCard">确定</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import { integralCategory, integralCreate, categorySetShow } from '@/api/marketing';
    import { changeListApi, allLabelApi, cascaderListApi } from "@/api/product";
    import { storegoodsList, giftadd, giftList, gifdetailList, gifaddcardnumber, giftupdatestatus, giftexport } from "@/api/user";
    import { Button } from 'view-design';


    export default {
        name: 'user_group',
        data () {
            return {
                addCardModal: false,         // 新增制卡弹窗显隐
                addCardForm: {
                    total_num: 1               // 制卡数量
                },
                addCardRules: {
                    total_num: [
                        { required: true, type: 'number', min: 1, message: '请输入至少1张', trigger: 'blur' }
                    ]
                },
                viewModal: false,          // 控制弹窗显隐
                viewQuery: {
                    use_keyword: '',
                    dis_keyword: '',
                    card_number: '',
                    status: '',
                    page: 1,
                    limit: 10
                },
                viewList: [],              // 弹窗内表格数据
                viewTotal: 0,
                viewColumns: [
                    { title: '编号', key: 'id', width: 80, align: 'center' },
                    { title: '卡号', key: 'card_number', align: 'center' },
                    {
                        title: '卡密',
                        key: 'card_password',
                        align: 'center',
                        render: (h, params) => {
                            const row = params.row;
                            const show = row._showPassword || false;
                            const stars = '*'.repeat((row.card_password || '').length || 8);

                            return h('div', [
                                h('span', {
                                    style: {
                                        fontFamily: 'monospace',
                                        letterSpacing: '1px',
                                        marginRight: '6px'
                                    }
                                }, show ? row.card_password : stars),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        icon: show ? 'ios-eye-off' : 'ios-eye',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.$set(row, '_showPassword', !show);
                                        }
                                    }
                                })
                            ]);
                        }
                    },
                    {
                        title: '头像',
                        key: 'avatar',
                        align: 'center',
                        render: (h, { row }) => {
                            return row.avatar
                                ? h('img', {
                                    attrs: {
                                        src: row.avatar,
                                        style: 'width:40px;height:40px;border-radius:50%;'
                                    }
                                })
                                : h('div', { style: 'width:40px;height:40px;line-height:40px;text-align:center;color:#ccc;' }, '');
                        }
                    },
                    { title: '使用用户', key: 'nickname', align: 'center' },
                    { title: '手机号', key: 'phone', align: 'center' },
                    { title: '分销员名称', key: 'dis_nickname', align: 'center' },
                    { title: '分销员电话', key: 'dis_phone', align: 'center' },
                    { title: '状态', key: 'status_text', align: 'center' },
                    { title: '使用时间', key: 'use_time_text', align: 'center' },
                    {
                        title: '操作',
                        key: 'action',
                        align: 'center',
                        width: 100,
                        render: (h, params) => {
                            const row = params.row;
                            // 只剩一张卡时强制禁用开关
                            const disabled = this.viewList.length === 1 && row.status === 1;
                            return h('i-switch', {
                                props: {
                                    value: +row.status,        // 默认 1（启用）
                                    'true-value': 0,
                                    'false-value': 1,
                                    size: 'large',
                                    disabled
                                },
                                scopedSlots: {
                                    open: () => h('span', '启用'),
                                    close: () => h('span', '禁用')
                                },
                                on: {
                                    'on-change': val => {
                                        this.changeCardStatus(row.id, val);
                                    }
                                }
                            });
                        }
                    }
                ],
                grid: { xl: 7, lg: 7, md: 12, sm: 24, xs: 24 },
                loading: false, loadingSave: false,
                columns1: [
                    { title: '编号', key: 'id', width: 80 },
                    { title: '礼品卡名称', key: 'name', minWidth: 100 },
                    { title: '总量', key: 'total_num', minWidth: 100 },
                    { title: '使用量', key: 'used_num', minWidth: 100 },
                    { title: '状态', slot: 'status', minWidth: 100 },
                    { title: '商品ID', key: 'goods_id', minWidth: 100 },
                    { title: '商品图', key: 'goods_image', minWidth: 100, render: (h, { row }) => h('img', { attrs: { src: row.goods_image, style: 'width:60px;height:60px;' } }) },
                    { title: '商品名称', key: 'goods_name', minWidth: 100 },
                    // { title: '创建时间', key: 'create_time', minWidth: 100 },
                    { title: '操作', slot: 'action', width: 120 }
                ],
                sortFrom: { page: 1, limit: 10, name: '', goods_name: '' },
                list: [], total: 0,

                /* 新增礼品卡弹窗 */
                modal1: false,
                form: { name: '', num: 1, goods: { id: '', name: '', image: '' }, is_show: 1 },
                rules: {
                    name: [{ required: true, message: '请输入礼品卡名称' }],
                    num: [{ required: true, type: 'number', min: 1, message: '制卡数量至少为 1' }]
                },

                /* 选择商品弹窗 */
                modal2: false,
                goodsSearch: { page: 1, limit: 10, cate_id: '', tag_id: '', keyword: '' },
                goodsList: [], goodsTotal: 0,
                goodsColumns: [
                    { title: '商品ID', key: 'id' },
                    {
                        title: '图片',
                        key: 'image',
                        render: (h, { row }) => {
                            return h('img', {
                                attrs: {
                                    src: row.image,
                                    style: 'width:40px;height:40px;object-fit:cover;border-radius:4px;'
                                }
                            });
                        }
                    },
                    { title: '商品名称', key: 'store_name' },
                    { title: '操作', slot: 'action', align: 'center' }
                ],
                cateOptions: [{ id: 1, name: '儿科门诊' }, { id: 2, name: '家庭心身健康' }],
                tagOptions: [{ id: 1, name: '次卡商品' }]
            };
        },
        computed: {
            ...mapState('admin/layout', ['isMobile']),
            labelWidth () { return this.isMobile ? undefined : 85; },
            labelPosition () { return this.isMobile ? 'top' : 'right'; }
        },
        created () {
            this.getList();
        },
        methods: {
            handleAddCard () {
                this.loadingSave = true;
                const params = {
                    gift_card_id: this.viewQuery.gift_card_id, // 当前礼品卡 id
                    total_num: this.addCardForm.total_num
                };
                gifaddcardnumber(params).then(res => {
                    this.$Message.success(res.msg || '新增成功');
                    this.addCardModal = false;
                    this.getViewList();
                    this.getList();        // 刷新当前礼品卡的卡列表
                }).finally(() => {
                    this.loadingSave = false;
                });
            },
            addViewList () {
                this.$refs.addCardForm && this.$refs.addCardForm.resetFields();
                this.addCardForm.total_num = 1;
                this.addCardModal = true;
            },
            detail (row, index) {
                // 打开弹窗并默认带上当前礼品卡 id（或整行）
                this.viewQuery.gift_card_id = row.id;
                this.viewModal = true;
                this.getViewList();
            },
            getViewList () {
                gifdetailList(this.viewQuery).then(res => {
                    this.viewList = res.data.list.map(item => ({
                        ...item,
                        _showPassword: false // 控制卡密显示隐藏
                    }));
                    this.viewTotal = res.data.count;
                })


            },
            viewPageChange (p) {
                this.viewQuery.page = p;
                this.getViewList();
            },
            exportData () {
                const query = this.viewQuery;
                giftexport(query.gift_card_id).then(res => {
                    const url = res.data
                    window.open(url, '_blank');
                })
            },
            /* 主列表 */
            sortSearchs () {
                this.sortFrom.page = 1;
                this.getList();
            },
            getList () {
                this.loading = true;
                giftList(this.sortFrom).then(res => {
                    this.list = res.data.list;
                    this.total = res.data.count;
                }).finally(() => { this.loading = false; });
            },
            pageChange (index) {
                this.sortFrom.page = index;
                this.getList();
            },
            // onchangeIsShow (row) {
            //     categorySetShow({ id: row.id, status: row.status })
            //         .then(res => this.$Message.success(res.msg))
            //         .catch(res => this.$Message.error(res.msg));
            // },
            del (row, tit, idx) {
                this.$Modal.confirm({
                    title: tit,
                    content: '确定要删除吗？',
                    onOk: () => {
                        this.$request({ url: `/marketing/integral/category/${row.id}`, method: 'DELETE' })
                            .then(res => {
                                this.$Message.success(res.msg);
                                this.list.splice(idx, 1);
                                if (!this.list.length && this.sortFrom.page > 1) {
                                    this.sortFrom.page--;
                                    this.getList();
                                }
                            });
                    }
                });
            },

            /* 新增礼品卡弹窗 */
            openAddModal () {
                this.resetForm();
                this.modal1 = true;
            },
            closeModal1 () {
                this.modal1 = false;
            },
            resetForm () {
                this.$refs.form && this.$refs.form.resetFields();
                this.form = { name: '', num: 1, goods: { id: '', name: '', image: '' }, is_show: 1 };
            },
            handleSubmit () {
                const params = {
                    name: this.form.name,
                    total_num: this.form.num,
                    goods_id: this.form.goods.id,
                    status: this.form.is_show
                };

                giftadd(params).then(res => {
                    this.$Message.success(res.msg);
                    this.getList();
                    this.modal1 = false; // ✅ 关闭弹窗
                }).catch(err => {
                    this.$Message.error(err.msg || '添加失败');
                });
            },

            /* 选择商品弹窗 */
            openGoodsModal () {
                this.modal2 = true;
                this.getGoodsList();
            },
            getGoodsList () {
                storegoodsList(this.goodsSearch).then(res => {
                    this.goodsList = res.data.list;
                    this.goodsTotal = res.data.count;
                })
            },
            goodsPageChange (p) {
                this.goodsSearch.page = p;
                this.getGoodsList();
            },
            selectGoods (row) {
                this.form.goods = { id: row.id, name: row.name, image: row.image };
                this.modal2 = false;
            },
            changeCardStatus (id, status) {
                console.log(status, '77777')
                if (status === 1) {
                    status = -1
                }
                const data = {
                    id: id,
                    status: status
                }
                console.log(data, '罗鑫')
                giftupdatestatus(data).then(res => {
                    this.$Message.success(res.msg || '状态修改成功');
                    // 刷新当前查看礼品卡列表
                    this.getViewList();
                }).catch(err => {
                    this.$Message.error(err.msg || '状态修改失败');
                    // 失败后重新拉一次保持数据一致性
                    this.getViewList();
                });
            }
        }
    };
</script>

<style scoped lang="stylus">
    .input-add
        width 250px
        margin-right 14px
    .selected-goods
        display flex
        align-items center
        img
            width 40px
            height 40px
            object-fit cover
            margin-right 8px
        button
            margin-left auto
</style>