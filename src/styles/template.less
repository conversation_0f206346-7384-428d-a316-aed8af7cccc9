.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around-center {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-x-center {
  display: flex;
  justify-content: center;
}

.flex-y-center {
  display: flex;
  align-items: center;
}

.grid-column-2 {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  grid-template-rows: none;
}

.grid-column-3 {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  grid-template-rows: none;
}

.grid-column-4 {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  grid-template-rows: none;
}

.grid-column-5 {
  display: grid;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  grid-template-rows: none;
}

.abs-lb {
  position: absolute;
  left: 0;
  bottom: 0;
}

.abs-lt {
  position: absolute;
  left: 0;
  top: 0;
}

.abs-rt {
  position: absolute;
  right: 0;
  top: 0;
}

.abs-y-center {
  position: absolute;
  top: 50%;
  --un-translate-y: -50%;
  transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y))
    translateZ(var(--un-translate-z)) rotate(var(--un-rotate))
    rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y))
    rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y))
    scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y))
    scaleZ(var(--un-scale-z));
}

.fixed-lb {
  position: fixed;
  left: 0;
  bottom: 0;
}

.fixed-lt {
  position: fixed;
  left: 0;
  top: 0;
}

.p-30px {
  padding: 30px;
}

.px {
  padding-left: 32px;
  padding-right: 32px;
}

.px-16px {
  padding-left: 16px;
  padding-right: 16px;
}

.px-24px {
  padding-left: 24px;
  padding-right: 24px;
}

.px-44px {
  padding-left: 44px;
  padding-right: 44px;
}

.py-16px {
  padding-top: 16px;
  padding-bottom: 16px;
}

.pb-26px {
  padding-bottom: 26px;
}

.pt-100px {
  padding-top: 100px;
}

.pt-10 {
	padding-top: 10px;
}

.pt-20,
.pt-20px {
  padding-top: 20px;
}

.pt-320px {
  padding-top: 320px;
}

.mb-40,
.mb-40px {
  margin-bottom: 40px;
}

.ml-20{
  margin-left: 10px;
}

.ml-20px {
	margin-left: 20px;
}

.mt-100px {
  margin-top: 100px;
}

.mt-200px {
  margin-top: 200px;
}

.mt-20,
.mt-20px {
  margin-top: 20px;
}

.mt-60px {
  margin-top: 60px;
}

.mt-6,
.mt-6px {
  margin-top: 6px;
}

.mt-80px {
  margin-top: 80px;
}

.mt-8,
.mt-8px {
  margin-top: 8px;
}

.inline {
  display: inline;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.hidden {
  display: none;
}

.bg-w111-2d8cf0 {
  background-color: #2d8cf0;
}

.bg--w111-000 {
  @un-bg-opacity: 1;
  background-color: rgba(0, 0, 0, @un-bg-opacity);
}

.bg--w111-bbb {
  @un-bg-opacity: 1;
  background-color: rgba(187, 187, 187, @un-bg-opacity);
}

.bg--w111-ccc {
  @un-bg-opacity: 1;
  background-color: rgba(204, 204, 204, @un-bg-opacity);
}

.bg--w111-d8d8d8 {
  @un-bg-opacity: 1;
  background-color: rgba(216, 216, 216, @un-bg-opacity);
}

.bg--w111-E93323 {
  @un-bg-opacity: 1;
  background-color: rgba(233, 51, 35, @un-bg-opacity);
}

.bg--w111-eee {
  @un-bg-opacity: 1;
  background-color: rgba(238, 238, 238, @un-bg-opacity);
}

.bg--w111-f5f5f5 {
  @un-bg-opacity: 1;
  background-color: rgba(245, 245, 245, @un-bg-opacity);
}

.bg--w111-F7E9CD {
  @un-bg-opacity: 1;
  background-color: rgba(247, 233, 205, @un-bg-opacity);
}

.bg--w111-FAAD14 {
  @un-bg-opacity: 1;
  background-color: rgba(250, 173, 20, @un-bg-opacity);
}

.bg--w111-FCEAE9 {
  @un-bg-opacity: 1;
  background-color: rgba(252, 234, 233, @un-bg-opacity);
}

.bg--w111-FCF6E5 {
  @un-bg-opacity: 1;
  background-color: rgba(252, 246, 229, @un-bg-opacity);
}

.bg--w111-FFF0D1 {
  background-color: #fff0d1;
}

.bg--w111-fff {
  @un-bg-opacity: 1;
  background-color: rgba(255, 255, 255, @un-bg-opacity);
}

.bg--w111-fff-s111-80 {
  background-color: rgba(255, 255, 255, 0.8);
}

.bg-w111-F3F9FF{
	background-color: #F3F9FF;
}

.bg-w111-F9F9F9{
	background-color: #F9F9F9;
}

.bg-hui {
  background-color: #ccc;
}

.bg-primary-con {
  background-color: var(--primary-theme-con);
}

.bg-primary-light {
  background-color: var(--primary-theme-light);
}
.mx-34 {
  margin: 0 17px;
}
.b,
.border {
  border-width: 1px;
}
.border-b-F0F1F5 {
	border-bottom: 1px solid #F0F1F5;
}
.border-b {
  border-bottom-width: 1px;
}

.b-w111-F0F1F5{
  border-color:#F0F1F5
}

.b-w111-E7E7E7{
  border-color:#E7E7E7
}

.b--w111-ccc {
  @un-border-opacity: 1;
  border-color: rgba(204, 204, 204, @un-border-opacity);
}

.rd-5px {
  border-radius: 5px;
}

.rd-2px {
  border-radius: 2px;
}

.rd-7px {
  border-radius: 7px;
}

.rd-12px {
  border-radius: 12px;
}

.rd-14px {
  border-radius: 14px;
}
.rd-8px {
  border-radius: 8px;
}
.rd-16 {
  border-radius: 128px;
}

.rd-16px {
  border-radius: 16px;
}
.rd-18px {
  border-radius: 18px;
}


.rd-20px {
  border-radius: 20px;
}

.rd-24 {
  border-radius: 192px;
}

.rd-24px {
  border-radius: 24px;
}

.rd-24px {
  border-radius: 24px;
}

.rd-28px {
  border-radius: 28px;
}

.rd-2px {
  border-radius: 2px;
}

.rd-30 {
  border-radius: 240px;
}

.rd-30px {
  border-radius: 30px;
}

.rd-32 {
  border-radius: 256px;
}

.rd-36px {
  border-radius: 36px;
}

.rd-36px {
  border-radius: 36px;
}

.rd-3px {
  border-radius: 3px;
}

.rd-40px {
  border-radius: 40px;
}

.rd-44px {
  border-radius: 44px;
}

.rd-4px {
  border-radius: 4px;
}

.rd-50-p111- {
  border-radius: 50%;
}

.rd-50px {
  border-radius: 50px;
}

.rd-6px {
  border-radius: 6px;
}

.rd-8px {
  border-radius: 8px;
}

.rd-8px {
  border-radius: 8px;
}
.rd-b-16px {
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.rd-b-20px {
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

.rd-b-24px {
  border-bottom-left-radius: 24px;
  border-bottom-right-radius: 24px;
}

.rd-b-32px {
  border-bottom-left-radius: 32px;
  border-bottom-right-radius: 32px;
}

.rd-l-32px {
  border-top-left-radius: 32px;
  border-bottom-left-radius: 32px;
}

.rd-r-32px {
  border-top-right-radius: 32px;
  border-bottom-right-radius: 32px;
}
.rd-t-10px {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}


.rd-t-20px {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.rd-t-24px {
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}

.rd-t-40px {
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
}

.rd-rt-12px {
  border-top-right-radius: 12px;
}

.b-solid {
  border-style: solid;
}

.fs-24,
.text-24px {
  font-size: 24px;
}

.fs-28,
.text-28px {
  font-size: 28px;
}

.fs-30,
.text-30px {
  font-size: 30px;
}

.fs-38 {
  font-size: 38px;
}

.fw-500 {
  font-weight: 500;
}

.fw-600 {
  font-weight: 600;
}

.fw-700,
.fw-bold {
  font-weight: 700;
}

.lh-21px {
  line-height: 21px;
}
.lh-22px {
  line-height: 22px;
}
.lh-24px {
  line-height: 24px;
}

.lh-26px {
  line-height: 26px;
}

.lh-28px {
  line-height: 28px;
}

.lh-30px {
  line-height: 30px;
}

.lh-32px {
  line-height: 32px;
}

.lh-14px {
  line-height: 14px;
}

.lh-16px {
  line-height: 16px;
}

.lh-17px {
  line-height: 17px;
}

.lh-36px {
  line-height: 36px;
}

.lh-38px {
  line-height: 38px;
}

.lh-40px {
  line-height: 40px;
}

.lh-42px {
  line-height: 42px;
}

.lh-44px {
  line-height: 44px;
}

.lh-48px {
  line-height: 48px;
}

.lh-25px {
  line-height: 25px;
}

.lh-26px {
  line-height: 26px;
}
.lh-52px {
  line-height: 52px;
}

.lh-56px {
  line-height: 56px;
}

.lh-80px {
  line-height: 80px;
}

.line-through {
  text-decoration-line: line-through;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-wlll-fff{
	color: #ffffff;
}

.text-wlll-515A6E{
	color: #515A6E;
}

.text-wlll-1890FF{
	color: #1890FF;
}

.text-wlll-2d8cf0{
	color: #2d8cf0;
}

.text-wlll-F7BA1E{
	color: #F7BA1E;
}

.text-wlll-C0C4CC{
	color: #C0C4CC;
}

.text-wlll-909399{
	color: #909399 !important;
}

.text-wlll-303133{
	color: #303133;
}

.text-wlll-f5222d{
	color: #f5222d;
}

.text--w111-222 {
  @un-text-opacity: 1;
  color: rgba(34, 34, 34, @un-text-opacity);
}

.text--w111-282828 {
  @un-text-opacity: 1;
  color: rgba(40, 40, 40, @un-text-opacity);
}

.text--w111-333 {
  @un-text-opacity: 1;
  color: rgba(51, 51, 51, @un-text-opacity);
}

.text--w111-3d3d3d {
  @un-text-opacity: 1;
  color: rgba(61, 61, 61, @un-text-opacity);
}

.text--w111-666 {
  @un-text-opacity: 1;
  color: rgba(102, 102, 102, @un-text-opacity);
}

.text--w111-763B04 {
  @un-text-opacity: 1;
  color: rgba(118, 59, 4, @un-text-opacity);
}

.text--w111-7E4B06 {
  @un-text-opacity: 1;
  color: rgba(126, 75, 6, @un-text-opacity);
}

.text--w111-888 {
  @un-text-opacity: 1;
  color: rgba(136, 136, 136, @un-text-opacity);
}

.text--w111-8e8e8e {
  @un-text-opacity: 1;
  color: rgba(142, 142, 142, @un-text-opacity);
}

.text--w111-999 {
  @un-text-opacity: 1;
  color: rgba(153, 153, 153, @un-text-opacity);
}

.text--w111-aaa {
  @un-text-opacity: 1;
  color: rgba(170, 170, 170, @un-text-opacity);
}

.text--w111-ccc {
  @un-text-opacity: 1;
  color: #ccc;
}

.text--w111-b3b3b3 {
  @un-text-opacity: 1;
  color: rgba(179, 179, 179, @un-text-opacity);
}

.text--w111-e93323 {
  @un-text-opacity: 1;
  color: rgba(233, 51, 35, @un-text-opacity);
}

.text--w111-FAAD14 {
  @un-text-opacity: 1;
  color: rgba(250, 173, 20, @un-text-opacity);
}

.text--w111-FFD89C {
  @un-text-opacity: 1;
  color: rgba(255, 216, 156, @un-text-opacity);
}

.text--w111-fff {
  @un-text-opacity: 1;
  color: rgba(255, 255, 255, @un-text-opacity);
}

.text-primary-con {
  color: var(--primary-theme-con);
}
.cup {
  cursor: pointer;
}
.flex {
  display: flex;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-33 {
  flex: 0 0 33.33%;
}

.flex-wrap {
  flex-wrap: wrap;
}

.grid-gap-10px {
  grid-gap: 10px;
  gap: 10px;
}

.grid-gap-18px {
  grid-gap: 18px;
  gap: 18px;
}

.grid-gap-20px {
  grid-gap: 20px;
  gap: 20px;
}

.grid-gap-22px {
  grid-gap: 22px;
  gap: 22px;
}

.grid-gap-24px {
  grid-gap: 24px;
  gap: 24px;
}

.grid-gap-36px {
  grid-gap: 36px;
  gap: 36px;
}

.grid-gap-x-10px {
  grid-column-gap: 10px;
  -moz-column-gap: 10px;
  column-gap: 10px;
}

.grid-gap-x-22px {
  grid-column-gap: 22px;
  -moz-column-gap: 22px;
  column-gap: 22px;
}

.grid-gap-x-19px {
  grid-column-gap: 19px;
  -moz-column-gap: 19px;
  column-gap: 19px;
}

.grid-gap-x-38px {
  grid-column-gap: 38px;
  -moz-column-gap: 38px;
  column-gap: 38px;
}

.grid-gap-y-20px {
  grid-row-gap: 20px;
  row-gap: 20px;
}

.grid-gap-y-26px {
  grid-row-gap: 26px;
  row-gap: 26px;
}
.grid-gap-y-27px {
  grid-row-gap: 27px;
  row-gap: 27px;
}
.grid-gap-y-32px {
  grid-row-gap: 32px;
  row-gap: 32px;
}

.grid-gap-y-54px {
  grid-row-gap: 54px;
  row-gap: 54px;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.relative {
  position: relative;
}

.h-13{
	height: 13px;
}

.h-14 {
  height: 14px;
}

.h-23{
	height: 23px;
}
.h-27{
	height: 27px;
}
.h-36{
	height: 36px;
}
.h-72{
	height: 72px;
}

.h-100 {
  height: 100px;
}

.h-100-p111-,
.h-full {
  height: 100%;
}

.h-1000 {
  height: 1000px;
}

.h-104 {
  height: 52px;
}

.h-104px {
  height: 104px;
}

.h-106 {
  height: 53px;
}

.h-108 {
  height: 54px;
}

.h-114 {
  height: 57px;
}

.h-116 {
  height: 58px;
}

.h-120 {
  height: 60px;
}

.h-124 {
  height: 62px;
}

.h-128 {
  height: 64px;
}

.h-136 {
  height: 68px;
}

.h-139 {
  height: 139px;
}

.h-140 {
  height: 140px;
}

.h-142 {
  height: 142px;
}

.h-148 {
  height: 148px;
}

.h-152 {
  height: 152px;
}

.h-160 {
  height: 160px;
}

.h-164 {
  height: 164px;
}

.h-170 {
  height: 170px;
}

.h-172 {
  height: 172px;
}

.h-176 {
  height: 176px;
}
.h-18 {
  height: 18px;
}


.h-180 {
  height: 180px;
}

.h-190 {
  height: 190px;
}

.h-192 {
  height: 192px;
}

.h-198 {
  height: 198px;
}

.h-20 {
  height: 20px;
}

.h-200 {
  height: 200px;
}

.h-204 {
  height: 204px;
}

.h-210 {
  height: 210px;
}

.h-212 {
  height: 212px;
}

.h-222 {
  height: 222px;
}

.h-24 {
  height: 24px;
}

.h-240 {
  height: 240px;
}

.h-266 {
  height: 266px;
}

.h-282 {
  height: 282px;
}

.h-26 {
  height: 26px;
}

.h-28 {
  height: 28px;
}

.h-296 {
  height: 296px;
}

.h-3 {
  height: 3px;
}

.h-30 {
  height: 30px;
}

.h-32 {
  height: 32px;
}

.h-33 {
  height: 33px;
}

.h-338 {
  height: 338px;
}

.h-34,
.h-34px {
  height: 34px;
}

.h-338 {
  height: 338px;
}

.h-344 {
  height: 344px;
}

.h-36 {
  height: 36px;
}

.h-38 {
  height: 38px;
}

.h-4 {
  height: 4px;
}

.h-40 {
  height: 40px;
}

.h-400 {
  height: 400px;
}

.h-44 {
  height: 44px;
}

.h-46 {
  height: 46px;
}

.h-47 {
  height: 47px;
}

.h-48 {
  height: 48px;
}

.h-50 {
  height: 50px;
}

.h-52 {
  height: 52px;
}

.h-56 {
  height: 56px;
}

.h-58 {
  height: 58px;
}

.h-6 {
  height: 6px;
}

.h-64 {
  height: 64px;
}

.h-66 {
  height: 66px;
}

.h-644 {
  height: 644px;
}

.h-68 {
  height: 68px;
}

.h-73 {
  height: 73px;
}

.h-76 {
  height: 76px;
}

.h-710 {
  height: 710px;
}

.h-72 {
  height: 72px;
}

.h-748 {
  height: 748px;
}

.h-750 {
  height: 750px;
}

.h-78 {
  height: 78px;
}

.h-80 {
  height: 80px;
}

.h-86 {
  height: 43px;
}

.h-88 {
  height: 44px;
}

.h-90 {
  height: 45px;
}

.h-96 {
  height: 48px;
}
.h-96px {
  height: 96px;
}

.h-480 {
  height: 480px;
}

.h-88px {
  height: 88px;
}

.h-150 {
  height: 150px;
}

.min-w-0 {
	min-width: 0;
}

.w-1 {
  width: 1px;
}

.w-14 {
  width: 14px;
}
.w-30{
	width: 30px;
}
.w-36{
	width: 36px;
}
.w-60 {
  width: 60px;
}
.w-64 {
  width: 64px;
}
.w-65 {
  width: 65px;
}
.w-70 {
  width: 70px;
}
.w-72{
	width: 72px;
}
.w-100 {
  width: 50px;
}

.w-100-p111-,
.w-full {
  width: 100%;
}

.w-50-full {
	width: 50%;
}

.w-102 {
  width: 102px;
}

.w-104 {
  width: 104px;
}

.w-108 {
  width: 108px;
}

.w-112 {
  width: 112px;
}

.w-116 {
  width: 116px;
}

.w-118 {
  width: 118px;
}

.w-119 {
  width: 119px;
}

.w-120 {
  width: 120px;
}

.w-124 {
  width: 124px;
}

.w-128 {
  width: 128px;
}

.w-130 {
  width: 130px;
}

.w-132 {
  width: 132px;
}

.w-136 {
  width: 68px;
}

.w-138 {
  width: 138px;
}

.w-140 {
  width: 140px;
}

.w-142 {
  width: 142px;
}

.w-144 {
  width: 144px;
}

.w-148 {
  width: 148px;
}

.w-150 {
  width: 150px;
}

.w-154 {
  width: 154px;
}

.w-160 {
  width: 160px;
}

.w-162 {
  width: 162px;
}

.w-164 {
  width: 164px;
}

.w-168 {
  width: 168px;
}

.w-172 {
  width: 172px;
}

.w-176 {
  width: 176px;
}

.w-18 {
  width: 18px;
}

.w-20 {
  width: 20px;
}

.w-184 {
  width: 184px;
}

.w-26 {
  width: 26px;
}

.w-28 {
  width: 28px;
}

.w-250 {
  width: 250px;
}

.w-237 {
  width: 237px;
}

.w-264 {
  width: 264px;
}

.w-280 {
  width: 280px;
}

.w-180 {
  width: 180px;
}

.w-186 {
  width: 186px;
}

.w-192 {
  width: 192px;
}

.w-198 {
  width: 198px;
}

.w-200 {
  width: 200px;
}

.w-204 {
  width: 204px;
}

.w-104 {
  width: 104px;
}
.w-208 {
  width: 208px;
}

.w-216 {
  width: 216px;
}

.w-218 {
  width: 218px;
}

.w-222 {
  width: 222px;
}

.w-22 {
  width: 22px;
}

.w-24,
.w-24px {
  width: 24px;
}

.w-240 {
  width: 240px;
}

.w-273 {
  width: 273px;
}

.w-278 {
  width: 278px;
}

.w-296 {
  width: 278px;
}

.w-331 {
  width: 331px;
}

.w-30 {
  width: 30px;
}

.w-32 {
  width: 32px;
}

.w-35 {
  width: 35px;
}

.w-316 {
  width: 316px;
}

.w-320 {
  width: 320px;
}

.w-324 {
  width: 324px;
}

.w-33 {
  width: 33px;
}

.w-338 {
  width: 338px;
}

.w-344 {
  width: 344px;
}

.w-346 {
  width: 346px;
}

.w-408{
   width: 408px;
}

.w-36 {
  width: 36px;
}

.w-40 {
  width: 40px;
}

.w-44 {
  width: 44px;
}

.w-360px {
  width: 360px;
}

.w-367 {
  width: 367px;
}

.w-381 {
  width: 381px;
}

.w-382 {
  width: 382px;
}

.w-400 {
  width: 400px;
}

.w-410 {
  width: 410px;
}

.w-422 {
  width: 422px;
}

.w-438 {
  width: 438px;
}

.w-454 {
  width: 454px;
}

.w-460 {
  width: 460px;
}

.w-462 {
  width: 462px;
}

.w-464 {
  width: 464px;
}

.w-48 {
  width: 48px;
}

.w-498 {
  width: 498px;
}

.w-504 {
  width: 504px;
}

.w-508 {
  width: 508px;
}

.w-538 {
  width: 538px;
}

.w-544 {
  width: 544px;
}

.w-52 {
  width: 52px;
}

.w-56 {
  width: 56px;
}
.w-68 {
  width: 68px;
}
.w-528 {
  width: 528px;
}
.w-560 {
  width: 560px;
}

.w-562 {
  width: 562px;
}

.w-600 {
  width: 600px;
}

.w-678 {
  width: 678px;
}

.w-686 {
  width: 686px;
}

.w-690 {
  width: 690px;
}

.w-710 {
  width: 710px;
}

.w-240 {
	width: 240px;
}

.w-300{
	width: 300px;
}

.w-72 {
  width: 72px;
}

.w-730 {
  width: 730px;
}

.w-76 {
  width: 76px;
}

.w-78 {
  width: 78px;
}

.w-80 {
  width: 80px;
}

.w-85 {
  width: 85px;
}

.w-88 {
  width: 88px;
}

.w-90 {
  width: 90px;
}

.w-303 {
  width: 303px;
}

.w-96 {
  width: 96px;
}

.w-screen {
  width: 100vw;
}

.fs-normal {
	font-style: normal;
}

.visible {
  visibility: visible;
}

.vertical-middle {
  vertical-align: middle;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.bottom-30px {
  bottom: 30px;
}

.bottom-54px {
  bottom: 54px;
}

.left-0 {
  left: 0;
}

.right-15px {
  right: 15px;
}

.z-1 {
  z-index: 1;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-999 {
  z-index: 999;
}

.transition {
  transition-property: color, background-color, border-color, outline-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, outline-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-property: color, background-color, border-color, outline-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.transform {
  transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y))
    translateZ(var(--un-translate-z)) rotate(var(--un-rotate))
    rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y))
    rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y))
    scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y))
    scaleZ(var(--un-scale-z));
}

.pb-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

.ml-3 {
  margin-left: 3px;
}

.ml-6 {
  margin-left: 6px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-12 {
  margin-left: 12px;
}

.ml-14 {
  margin-left: 14px;
}

.ml-16 {
  margin-left: 16px;
}

.ml-19 {
  margin-left: 19px;
}

.ml-22 {
  margin-left: 22px;
}

.ml-23 {
  margin-left: 23px;
}

.ml-24 {
  margin-left: 24px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-32 {
  margin-left: 32px;
}

.ml-34 {
  margin-left: 34px;
}

.ml-38 {
  margin-left: 38px;
}

.ml-45 {
  margin-left: 45px;
}

.ml-4 {
  margin-left: 4px;
}

.ml-40 {
  margin-left: 20px;
}

.ml-8 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 2px;
}

.mr-4 {
  margin-right: 4px;
}

.mr-6 {
  margin-right: 6px;
}

.mr-7 {
  margin-right: 7px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-12 {
  margin-right: 12px;
}

.mr-14 {
  margin-right: 14px;
}

.mr-16 {
  margin-right: 16px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-24 {
  margin-right: 24px;
}

.mr-32 {
  margin-right: 32px;
}

.mr-40 {
  margin-right: 40px;
}

.mr-74 {
  margin-right: 74px;
}

.mr-8 {
  margin-right: 8px;
}

.mt-2 {
  margin-top: 2px;
}

.mt-3 {
  margin-top: 3px;
}

.mt-5 {
  margin-top: 5px;
}

.mt-7 {
  margin-top: 7px;
}

.mt-9 {
  margin-top: 9px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-14 {
  margin-top: 14px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-18 {
  margin-top: 18px;
}

.mt-180 {
  margin-top: 180px;
}

.mt-24 {
  margin-top: 24px;
}

.mt-248 {
  margin-top: 248px;
}

.mt-26 {
  margin-top: 26px;
}

.mt-28 {
  margin-top: 28px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-32 {
  margin-top: 32px;
}

.mt-34 {
  margin-top: 34px;
}

.mt-36 {
  margin-top: 36px;
}

.mt-38 {
  margin-top: 38px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-22 {
  margin-top: 22px;
}

.mt-48 {
  margin-top: 48px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-52 {
  margin-top: 52px;
}

.mt-54 {
  margin-top: 54px;
}

.mt-64 {
  margin-top: 64px;
}

.mb-5 {
  margin-bottom: 5px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-14 {
  margin-bottom: 14px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-18 {
  margin-bottom: 18px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mb-26 {
  margin-bottom: 26px;
}

.mb-32 {
  margin-bottom: 32px;
}

.mb-34 {
  margin-bottom: 34px;
}

.mb-38 {
  margin-bottom: 38px;
}

.p-10 {
  padding: 10px;
}
.p-12 {
  padding: 12px;
}


.p-14 {
  padding: 14px;
}

.p-16 {
  padding: 16px;
}

.p-20 {
  padding: 20px;
}

.p-24 {
  padding: 24px;
}

.p-32 {
  padding: 32px;
}

.pl-6 {
  padding-left: 6px;
}
.pl-10 {
  padding-left: 10px;
}
.pl-12 {
  padding-left: 12px;
}
.pl-14 {
  padding-left: 14px;
}

.pl-16 {
  padding-left: 16px;
}

.pl-18 {
  padding-left: 18px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-24 {
  padding-left: 24px;
}

.pl-28 {
  padding-left: 28px;
}

.pl-32 {
  padding-left: 16px;
}

.pl-4 {
  padding-left: 4px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-23 {
  padding-left: 23px;
}

.pl-56 {
  padding-left: 56px;
}

.pl-8 {
  padding-left: 8px;
}

.pr-6 {
  padding-right: 6px;
}

.pr-8 {
  padding-right: 8px;
}

.pr-10 {
  padding-right: 10px;
}


.pr-12 {
  padding-right: 12px;
}

.pr-14 {
  padding-right: 14px;
}
.pr-15 {
  padding-right: 15px;
}

.pr-17 {
  padding-right: 17px;
}
.pr-20 {
  padding-right: 20px;
}

.pr-24 {
  padding-right: 24px;
}

.pr-28 {
  padding-right: 14px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-32 {
  padding-right: 32px;
}

.pr-40 {
  padding-right: 40px;
}

.pr-46 {
  padding-right: 46px;
}

.pr-50 {
  padding-right: 50px;
}

.pr-56 {
  padding-right: 56px;
}

.pt-3 {
  padding-top: 3px;
}
.pt-4 {
  padding-top: 4px;
}
.pt-6 {
  padding-top: 6px;
}
.pt-8 {
  padding-top: 8px;
}
.pt-11 {
  padding-top: 11px;
}
.pt-12 {
  padding-top: 12px;
}

.pt-14 {
  padding-top: 14px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-16 {
  padding-top: 16px;
}
.pt-17 {
  padding-top: 17px;
}

.pt-18 {
  padding-top: 18px;
}

.pt-22 {
  padding-top: 22px;
}

.pt-24 {
  padding-top: 24px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-32 {
  padding-top: 32px;
}

.pt-34 {
  padding-top: 34px;
}

.pt-18 {
  padding-top: 18px;
}

.pt-38 {
  padding-top: 38px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-48 {
  padding-top: 48px;
}

.pt-6 {
  padding-top: 6px;
}
.pb-4 {
  padding-bottom: 4px;
}
.pb-10 {
  padding-bottom: 10px;
}
.pb-12 {
  padding-bottom: 12px;
}
.pb-14 {
  padding-bottom: 14px;
}
.pb-16 {
  padding-bottom: 16px;
}
.pb-18 {
  padding-bottom: 18px;
}
.pb-19 {
  padding-bottom: 19px;
}
.pb-20 {
  padding-bottom: 20px;
}

.pb-22 {
  padding-bottom: 22px;
}

.pb-24 {
  padding-bottom: 24px;
}

.pb-16 {
  padding-bottom: 16px;
}

.pb-36 {
  padding-bottom: 36px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-8 {
  padding-bottom: 8px;
}

.mx-6 {
  margin: 0 6px;
}

.mx-10 {
  margin: 0 10px;
}

.mx-16 {
  margin: 0 16px;
}

.mx-20 {
  margin: 0 20px;
}

.mx-32 {
  margin: 0 32px;
}

.mx-17 {
  margin: 0 17px;
}

.mx-38 {
  margin: 0 38px;
}

.mx-8 {
  margin: 0 8px;
}

.my-16 {
  margin: 16px 0;
}

.my-20 {
  margin: 20px 0;
}

.my-24 {
  margin: 24px 0;
}
.px-4 {
  padding: 0 4px;
}
.px-12 {
  padding: 0 12px;
}

.px-16 {
  padding: 0 16px;
}

.px-10 {
  padding: 0 10px;
}

.px-24 {
  padding: 0 24px;
}

.px-28 {
  padding: 0 28px;
}

.px-32 {
  padding: 0 32px;
}

.px-24 {
  padding: 0 24px;
}

.px-6 {
  padding: 0 6px;
}

.px-8 {
  padding: 0 8px;
}

.py-16 {
  padding: 16px 0;
}
.py-24 {
  padding: 24px 0;
}
.py-28 {
  padding: 28px 0;
}

.py-30 {
  padding: 30px 0;
}

.py-32 {
  padding: 32px 0;
}

.red{
  color: #e93323;
}
.fs-9 {
  font-size: 9px !important;
}
.fs-10 {
  font-size: 10px;
}
.fs-11 {
  font-size: 11px;
}
.fs-12 {
  font-size: 12px !important;
}
.fs-14 {
  font-size: 14px;
}
.fs-15 {
  font-size: 15px;
}

.fs-16 {
  font-size: 16px;
}

.fs-18 {
  font-size: 18px;
}

.fs-20 {
  font-size: 20px;
}

.fs-22 {
  font-size: 22px;
}

.fs-13 {
  font-size: 13px;
}

.fs-32 {
  font-size: 16px;
}

.fs-34 {
  font-size: 34px;
}

.fs-42 {
  font-size: 42px;
}

.fs-44 {
  font-size: 44px;
}

.fs-48 {
  font-size: 48px;
}

.fs-60 {
  font-size: 60px;
}

.t-f5 {
	top: -5px;
}

.r-f5 {
	right: -5px;
}

.text-line {
  text-decoration: line-through;
}

.SemiBold{
	font-family: SemiBold;
}

.overflow{
	overflow: hidden;
}

.white-space-normal {
  white-space: normal;
}