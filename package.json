{"name": "iview-admin-pro-template", "version": "2.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --open  --mode=dev", "dev": "npm run serve --mode=dev", "build": "vue-cli-service build --mode=production"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@babel/runtime": "^7.2.0", "@form-create/iview": "^1.0.14", "async-validator": "^3.4.0", "awe-dnd": "^0.3.4", "axios": "^0.18.0", "better-scroll": "^1.12.1", "clipboard": "^2.0.6", "codemirror": "^5.38.0", "cos-js-sdk-v5": "^0.5.27", "crypto-js": "^4.1.1", "dayjs": "^1.8.9", "echarts": "^4.1.0", "element-ui": "^2.13.2", "emoji-awesome": "0.0.2", "file-saver": "^2.0.5", "html2canvas": "^1.1.0", "image-conversion": "^2.1.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "lodash": "^4.17.10", "lowdb": "^1.0.0", "marked": "^0.3.9", "mockjs": "^1.0.1-beta3", "moment": "^2.29.1", "monaco-editor": "^0.19.3", "oss": "0.0.1", "print-js": "^1.6.0", "printjs": "^1.1.0", "qiniu-js": "^2.5.5", "qrcodejs2": "0.0.2", "qs": "^6.6.0", "quill": "^1.3.6", "sass-loader": "^11.1.1", "screenfull": "^4.0.0", "simplemde": "^1.11.2", "ua-parser-js": "^0.7.18", "v-viewer": "^1.4.2", "view-design": "^4.1.0", "vue": "^2.6.10", "vue-awesome-swiper": "^3.1.3", "vue-codemirror": "^4.0.6", "vue-happy-scroll": "^2.1.1", "vue-i18n": "^7.8.1", "vue-jsonp": "^2.0.0", "vue-pickers": "^2.5.3", "vue-puzzle-vcode": "^1.1.9", "vue-router": "^3.1.3", "vue-ydui": "^1.2.6", "vuedraggable": "^2.24.0", "vuescroll": "^4.16.1", "vuex": "^3.0.1", "vxe-table": "^3.4.14", "wangeditor": "^4.7.11", "webpack-dev-server": "^2.9.1", "xe-utils": "^3.5.2", "xlsx": "^0.15.6"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.2.0", "@kazupon/vue-i18n-loader": "^0.3.0", "@vue/cli-plugin-babel": "^3.0.0", "@vue/cli-plugin-eslint": "^3.0.0", "@vue/cli-plugin-unit-jest": "^3.2.3", "@vue/cli-service": "^3.0.0", "@vue/eslint-config-standard": "^3.0.0", "@vue/test-utils": "^1.0.0-beta.28", "babel-core": "7.0.0-bridge.0", "babel-jest": "^23.0.1", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^5.0.1", "html-webpack-plugin": "^4.5.2", "image-webpack-loader": "^8.1.0", "iview-loader": "^1.3.0", "less": "^2.7.3", "less-loader": "^4.1.0", "lint-staged": "^7.2.0", "script-loader": "^0.7.2", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "svg-sprite-loader": "^3.8.0", "text-loader": "0.0.1", "uglifyjs-webpack-plugin": "^1.1.1", "vue-clipboard2": "^0.3.1", "vue-lazyload": "^1.3.3", "vue-template-compiler": "^2.6.10", "vue-waterfall-easy": "^2.4.4"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}